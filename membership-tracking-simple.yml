name: "Simple Membership Tracking & Auto-Expiration System"
version: "1.0"
description: "Add membership duration tracking and automatic expiration to existing pro key system while maintaining simplicity"

# Current System (Keep Simple)
current_system:
  file: "pro-keys-hashed.json"
  structure: "Simple array of hashed keys"
  identification: "Key hash only"
  validation: "Key exists in array = Pro user"

# Enhanced System (Still Simple)
enhanced_system:
  file: "pro-keys-enhanced.json"
  structure: "Object with key details"
  identification: "Key hash only (no emails/usernames)"
  validation: "Key exists + not expired = Pro user"

# Data Structure Design
data_structure:
  format: "JSON"
  schema:
    version: "string"
    lastUpdated: "ISO timestamp"
    keys:
      "[key_hash]":
        status: "active|expired|suspended"
        createdAt: "ISO timestamp"
        expiresAt: "ISO timestamp" 
        lastUsed: "ISO timestamp"
        usageCount: "number"
        tier: "pro|premium"
        notes: "optional string"

# Example Enhanced Structure
example_data: |
  {
    "version": "2.0",
    "lastUpdated": "2025-06-13T16:52:04.000Z",
    "keys": {
      "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456": {
        "status": "active",
        "createdAt": "2025-01-01T00:00:00.000Z",
        "expiresAt": "2026-01-01T00:00:00.000Z",
        "lastUsed": "2025-06-13T16:52:04.000Z",
        "usageCount": 1247,
        "tier": "pro",
        "notes": "Annual subscription"
      },
      "f6e5d4c3b2a1098765432109876543210987654321fedcba0987654321fedcba": {
        "status": "expired",
        "createdAt": "2024-06-01T00:00:00.000Z",
        "expiresAt": "2025-06-01T00:00:00.000Z",
        "lastUsed": "2025-05-30T14:22:15.000Z",
        "usageCount": 892,
        "tier": "pro",
        "notes": "Monthly subscription - expired"
      }
    }
  }

# Implementation Plan
implementation:
  
  # Phase 1: Enhanced Pro Status Module
  phase_1:
    name: "Core Membership Tracking"
    duration: "3-5 days"
    files_to_modify:
      - "js/user/proStatus.js"
      - "config.js"
    
    tasks:
      - task: "Add membership config constants"
        file: "config.js"
        changes:
          - "Add MEMBERSHIP_CONFIG object"
          - "Define status types (active, expired, suspended)"
          - "Define tier types (pro, premium)"
          - "Set default expiration period (1 year)"
      
      - task: "Enhance pro status checking"
        file: "js/user/proStatus.js"
        changes:
          - "Update checkProStatus() to check expiration"
          - "Add getDetailedProStatus() function"
          - "Add checkMembershipExpiration() function"
          - "Add updateLastUsed() function"
          - "Add getMembershipTimeRemaining() function"
      
      - task: "Add automatic expiration handling"
        file: "js/user/proStatus.js"
        changes:
          - "Auto-transition expired keys to regular status"
          - "Show expiration warnings (7, 3, 1 days)"
          - "Track usage count on each status check"

  # Phase 2: UI Enhancements
  phase_2:
    name: "Simple UI Updates"
    duration: "2-3 days"
    files_to_modify:
      - "popup.js"
      - "styles/popup.css"
    
    tasks:
      - task: "Enhanced pro status display"
        file: "popup.js"
        changes:
          - "Show expiration date in status"
          - "Show days remaining"
          - "Show usage count"
          - "Add expiration warnings"
          - "Add 'Membership Expired' state"
      
      - task: "Simple membership info section"
        file: "popup.js"
        changes:
          - "Add collapsible membership details"
          - "Show: Status, Expires, Days Left, Usage"
          - "No complex dashboard - keep it minimal"
      
      - task: "Styling for membership info"
        file: "styles/popup.css"
        changes:
          - "Style for membership details section"
          - "Warning colors for expiration alerts"
          - "Expired state styling"

  # Phase 3: Background Expiration Checker
  phase_3:
    name: "Automatic Expiration Management"
    duration: "2-3 days"
    files_to_create:
      - "js/utils/membershipChecker.js"
    files_to_modify:
      - "popup.js"
      - "background.js"
    
    tasks:
      - task: "Create simple expiration checker"
        file: "js/utils/membershipChecker.js"
        changes:
          - "Check expiration on app startup"
          - "Show notifications for expired memberships"
          - "Handle automatic status transitions"
          - "Simple daily check (no complex intervals)"
      
      - task: "Integrate expiration checking"
        file: "popup.js"
        changes:
          - "Run expiration check on popup open"
          - "Show expiration notifications"
          - "Handle expired state UI"

# Technical Implementation Details
technical_details:
  
  # New Config Constants
  config_additions:
    file: "config.js"
    code: |
      // Simple Membership Configuration
      export const MEMBERSHIP_CONFIG = {
          STATUS: {
              ACTIVE: 'active',
              EXPIRED: 'expired',
              SUSPENDED: 'suspended'
          },
          TIERS: {
              PRO: 'pro',
              PREMIUM: 'premium'
          },
          DEFAULT_DURATION: 365 * 24 * 60 * 60 * 1000, // 1 year in milliseconds
          WARNING_DAYS: [7, 3, 1], // Days before expiration to show warnings
          API_ENDPOINT: 'https://calel33.github.io/json-validate/pro-keys-enhanced.json'
      };
  
  # Enhanced Pro Status Functions
  pro_status_enhancements:
    file: "js/user/proStatus.js"
    functions:
      - name: "getDetailedProStatus(proKey)"
        purpose: "Get comprehensive membership info for a key"
        returns: "Object with status, expiration, usage, etc."
      
      - name: "checkMembershipExpiration(proKey)"
        purpose: "Check if membership is expired and handle transition"
        returns: "Object with expiration status and actions taken"
      
      - name: "updateLastUsed(proKey)"
        purpose: "Update last used timestamp and usage count"
        returns: "Boolean success status"
      
      - name: "getMembershipTimeRemaining(proKey)"
        purpose: "Calculate days/hours remaining until expiration"
        returns: "Object with time remaining details"
      
      - name: "handleMembershipExpiration(proKey)"
        purpose: "Handle automatic transition from pro to regular"
        returns: "Object with transition details"

  # Simple UI Updates
  ui_enhancements:
    pro_status_display:
      current: "Pro Key: Valid ✓"
      enhanced: |
        Pro Key: Valid ✓
        Tier: PRO | Expires: Jan 1, 2026
        Days Left: 201 | Used: 1,247 times
    
    expiration_warnings:
      7_days: "⚠️ Pro membership expires in 7 days"
      3_days: "⚠️ Pro membership expires in 3 days"
      1_day: "🚨 Pro membership expires tomorrow"
      expired: "❌ Pro membership expired - Upgrade to continue"

  # Background Checker
  expiration_checker:
    file: "js/utils/membershipChecker.js"
    functionality:
      - "Check expiration on popup open"
      - "Show browser notifications for expiring memberships"
      - "Automatically update status from active to expired"
      - "Track expiration events for analytics"
      - "Simple daily check (no complex background processes)"

# Migration Strategy
migration:
  approach: "Backward Compatible"
  steps:
    1: "Keep existing pro-keys-hashed.json as fallback"
    2: "Create new pro-keys-enhanced.json with migration data"
    3: "Update proStatus.js to check enhanced format first"
    4: "If enhanced format fails, fallback to simple format"
    5: "Gradual migration as users use the extension"
  
  migration_logic: |
    // Check enhanced format first
    const enhancedData = await fetchEnhancedKeys();
    if (enhancedData && enhancedData.keys[proKey]) {
        return checkEnhancedMembership(proKey, enhancedData);
    }
    
    // Fallback to simple format
    const simpleKeys = await fetchSimpleKeys();
    if (simpleKeys.includes(proKey)) {
        return { isPro: true, status: 'active', legacy: true };
    }
    
    return { isPro: false, status: 'invalid' };

# File Structure Changes
file_changes:
  new_files:
    - "js/utils/membershipChecker.js"
  
  modified_files:
    - file: "config.js"
      changes: "Add MEMBERSHIP_CONFIG constants"
    
    - file: "js/user/proStatus.js"
      changes: "Enhanced membership checking and expiration handling"
    
    - file: "popup.js"
      changes: "Updated UI to show membership details and expiration info"
    
    - file: "styles/popup.css"
      changes: "Styling for membership info and expiration warnings"

# Testing Plan
testing:
  unit_tests:
    - "Test expiration date calculations"
    - "Test status transitions (active -> expired)"
    - "Test usage count tracking"
    - "Test warning threshold calculations"
  
  integration_tests:
    - "Test with valid active membership"
    - "Test with expired membership"
    - "Test with suspended membership"
    - "Test fallback to legacy format"
  
  user_scenarios:
    - "New pro user activation"
    - "Existing pro user with time remaining"
    - "Pro user approaching expiration"
    - "Pro user after expiration"
    - "Legacy user migration"

# Success Criteria
success_criteria:
  functionality:
    - "✅ Automatic expiration detection and handling"
    - "✅ Clear membership status display with time remaining"
    - "✅ Proactive expiration warnings"
    - "✅ Seamless transition from pro to regular status"
    - "✅ Usage tracking for analytics"
  
  user_experience:
    - "✅ No disruption to existing pro users"
    - "✅ Clear visibility of membership status"
    - "✅ Helpful expiration warnings"
    - "✅ Simple renewal process guidance"
  
  technical:
    - "✅ 100% backward compatibility"
    - "✅ No performance degradation"
    - "✅ Reliable expiration checking"
    - "✅ Clean migration path"

# Deployment Strategy
deployment:
  approach: "Gradual Rollout"
  phases:
    1: "Deploy with feature flag disabled"
    2: "Enable for beta users (10%)"
    3: "Enable for all users (100%)"
    4: "Monitor and optimize"
  
  rollback_plan:
    - "Disable enhanced membership checking"
    - "Revert to simple array validation"
    - "No data loss - enhanced data is additive"
    - "Users continue with existing functionality"

# Future Enhancements (Out of Scope)
future_features:
  simple_additions:
    - "Different expiration periods (monthly, yearly)"
    - "Suspension/reactivation functionality"
    - "Simple usage analytics dashboard"
    - "Bulk key management tools"
  
  advanced_features:
    - "Payment integration"
    - "Automated renewal"
    - "Team/organization keys"
    - "Advanced analytics and reporting"

# Notes
notes:
  - "Keep it simple - no emails, usernames, or complex user data"
  - "Use key hash as the only identifier"
  - "Maintain current system's simplicity and speed"
  - "Focus on essential membership tracking only"
  - "Backward compatibility is critical"
  - "No breaking changes to existing functionality" 