# CODE MODIFICATION ENFORCEMENT RULES

## ⚠️ **MANDATORY ENFORCEMENT:**

For *every* user request that involves writing, modifying, or debugging code in the HustlePlug project, the assistant's *first* action **must** be to call the **`search_code_advanced_code-index`** MCP tool.

### **Required Actions Before Code Changes:**

1. **🔍 SEARCH FIRST**: Use `search_code_advanced_code-index` to find relevant patterns
2. **📊 ANALYZE PATTERNS**: Understand existing implementation patterns  
3. **🎯 IDENTIFY FILES**: Locate all files that may be affected
4. **✅ VERIFY CONTEXT**: Ensure understanding of codebase structure

### **You may only produce or edit code *after* these tool calls and their successful results.**

## 📋 **Specific Search Requirements:**

### **For Bug Fixes:**
- Search for the error/issue pattern
- Search for related functionality 
- Search for similar implementations
- Example: `search_code_advanced_code-index "loadChatHistory"`

### **For New Features:**
- Search for similar existing features
- Search for integration patterns
- Search for storage/API patterns
- Example: `search_code_advanced_code-index "BaseManager"`

### **For Refactoring:**
- Search for all instances of code being changed
- Search for dependent code
- Search for similar patterns
- Example: `search_code_advanced_code-index "chrome.storage.local"`

## 🚫 **VIOLATIONS:**

The following actions are **PROHIBITED** without prior MCP search:

- ❌ Writing new JavaScript files
- ❌ Modifying existing .js files
- ❌ Adding HTML sections
- ❌ Changing CSS styles
- ❌ Updating configuration files
- ❌ Modifying storage patterns
- ❌ Adding new API calls
- ❌ Changing manager integrations

## ✅ **EXCEPTIONS:**

The following actions may proceed without MCP search:

- ✅ Reading/viewing existing files
- ✅ Creating documentation files
- ✅ Writing test plans
- ✅ Creating markdown files
- ✅ Explaining existing code

## 🎯 **ENFORCEMENT WORKFLOW:**

```
User Request → MCP Search → Pattern Analysis → Code Modification
     ↓              ↓              ↓              ↓
   Identify    Find Related    Understand     Follow Existing
   Intent      Code/Patterns   Architecture    Patterns
```

## 📝 **COMPLIANCE EXAMPLES:**

### ✅ **CORRECT Approach:**
```
User: "Fix the chat history saving issue"
Assistant: 
1. search_code_advanced_code-index "loadChatHistory"
2. search_code_advanced_code-index "saveAnalysis" 
3. search_code_advanced_code-index "chrome.storage.local"
4. Analyze patterns and create fix
```

### ❌ **INCORRECT Approach:**
```
User: "Fix the chat history saving issue"
Assistant: 
1. Immediately starts writing code without searching
```

## 🔧 **PROJECT-SPECIFIC PATTERNS TO ALWAYS SEARCH:**

- **Manager Patterns**: `BaseManager`, `PopupController`
- **Storage Patterns**: `chrome.storage.local`, `chrome.storage.sync`
- **UI Patterns**: `showSection`, `navigateToSection`
- **Event Patterns**: `addEventListenerTracked`
- **API Patterns**: `performAnalysis`, `fetch`
- **Error Patterns**: `handleError`, `showError`

## 📊 **QUALITY ASSURANCE:**

Every code modification must:
1. **Follow existing patterns** found via MCP search
2. **Integrate properly** with found architecture
3. **Use consistent naming** with discovered conventions
4. **Respect existing storage keys** and data structures
5. **Maintain error handling patterns** found in codebase

---

**Remember: Search First, Code Second. Always.**
