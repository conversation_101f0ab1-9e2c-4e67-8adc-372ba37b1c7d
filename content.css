/* Agent Hu<PERSON>le Pro Analyzer - Content Styles */

/* Analysis Panel Styles */
#agent-hustle-analysis-panel {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
    line-height: 1.5 !important;
    color: #333 !important;
    box-sizing: border-box !important;
}

#agent-hustle-analysis-panel * {
    box-sizing: border-box !important;
}

#agent-hustle-analysis-panel .analysis-content {
    font-size: 14px !important;
    line-height: 1.6 !important;
}

#agent-hustle-analysis-panel .analysis-content p {
    margin: 0 0 12px 0 !important;
    padding: 0 !important;
}

#agent-hustle-analysis-panel .analysis-content strong {
    font-weight: 600 !important;
    color: #333 !important;
}

#agent-hustle-analysis-panel .analysis-content em {
    font-style: italic !important;
    color: #555 !important;
}

#agent-hustle-analysis-panel .analysis-content code {
    background: #f8f9fa !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace !important;
    font-size: 13px !important;
    color: #e83e8c !important;
}

#agent-hustle-analysis-panel .analysis-content h3,
#agent-hustle-analysis-panel .analysis-content h4,
#agent-hustle-analysis-panel .analysis-content h5 {
    margin: 16px 0 8px 0 !important;
    font-weight: 600 !important;
    color: #333 !important;
}

#agent-hustle-analysis-panel .analysis-content h3 {
    font-size: 16px !important;
}

#agent-hustle-analysis-panel .analysis-content h4 {
    font-size: 15px !important;
}

#agent-hustle-analysis-panel .analysis-content h5 {
    font-size: 14px !important;
}

#agent-hustle-analysis-panel .analysis-content ul,
#agent-hustle-analysis-panel .analysis-content ol {
    margin: 8px 0 12px 20px !important;
    padding: 0 !important;
}

#agent-hustle-analysis-panel .analysis-content li {
    margin: 4px 0 !important;
    padding: 0 !important;
}

/* Tool Calls Section */
#agent-hustle-analysis-panel .tool-calls-section {
    margin-top: 16px !important;
    padding-top: 16px !important;
    border-top: 1px solid #e9ecef !important;
}

#agent-hustle-analysis-panel .tool-calls-section h5 {
    margin: 0 0 12px 0 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #667eea !important;
}

#agent-hustle-analysis-panel .tool-call {
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px !important;
    margin-bottom: 8px !important;
}

#agent-hustle-analysis-panel .tool-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
}

#agent-hustle-analysis-panel .tool-name {
    font-weight: 600 !important;
    font-size: 13px !important;
    color: #495057 !important;
}

#agent-hustle-analysis-panel .tool-status.success {
    color: #28a745 !important;
}

#agent-hustle-analysis-panel .tool-status.error {
    color: #dc3545 !important;
}

#agent-hustle-analysis-panel .tool-content {
    font-size: 13px !important;
    color: #6c757d !important;
    background: white !important;
    padding: 8px !important;
    border-radius: 4px !important;
    border: 1px solid #dee2e6 !important;
}

/* Selection Tooltip Styles */
#agent-hustle-selection-tooltip {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: auto !important;
    z-index: 10000 !important;
}

#agent-hustle-selection-tooltip:hover {
    transform: scale(1.05) !important;
}

/* Notification Styles */
.agent-hustle-notification {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: none !important;
    z-index: 10001 !important;
}

/* Floating Action Button Styles */
#agent-hustle-fab {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    pointer-events: auto !important;
}

#agent-hustle-fab:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4) !important;
}

#agent-hustle-fab:active {
    transform: scale(0.95) !important;
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    to {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    #agent-hustle-analysis-panel {
        width: calc(100vw - 40px) !important;
        max-width: 350px !important;
        right: 20px !important;
        left: auto !important;
    }
    
    #agent-hustle-fab {
        bottom: 20px !important;
        right: 20px !important;
        width: 50px !important;
        height: 50px !important;
        font-size: 20px !important;
    }
    
    #agent-hustle-selection-tooltip {
        font-size: 11px !important;
        padding: 6px 10px !important;
    }
}

@media (max-width: 480px) {
    #agent-hustle-analysis-panel {
        width: calc(100vw - 20px) !important;
        right: 10px !important;
        top: 10px !important;
        max-height: calc(100vh - 20px) !important;
    }
    
    .agent-hustle-notification {
        max-width: calc(100vw - 40px) !important;
        font-size: 13px !important;
        padding: 10px 16px !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #agent-hustle-analysis-panel {
        background: #1a1a1a !important;
        border-color: #333 !important;
        color: #e0e0e0 !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content {
        color: #e0e0e0 !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content strong {
        color: #fff !important;
    }
    
    #agent-hustle-analysis-panel .analysis-content code {
        background: #2d2d2d !important;
        color: #ff6b9d !important;
    }
    
    #agent-hustle-analysis-panel .tool-call {
        background: #2d2d2d !important;
        border-color: #444 !important;
    }
    
    #agent-hustle-analysis-panel .tool-content {
        background: #1a1a1a !important;
        border-color: #444 !important;
        color: #ccc !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    #agent-hustle-analysis-panel {
        border: 2px solid #000 !important;
    }
    
    #agent-hustle-selection-tooltip {
        border: 2px solid #fff !important;
    }
    
    .agent-hustle-notification {
        border: 2px solid #fff !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    #agent-hustle-analysis-panel,
    #agent-hustle-selection-tooltip,
    .agent-hustle-notification,
    #agent-hustle-fab {
        animation: none !important;
        transition: none !important;
    }
    
    #agent-hustle-fab:hover {
        transform: none !important;
    }
}

/* Print Styles */
@media print {
    #agent-hustle-analysis-panel,
    #agent-hustle-selection-tooltip,
    .agent-hustle-notification,
    #agent-hustle-fab {
        display: none !important;
    }
}

/* Focus Styles for Accessibility */
#agent-hustle-analysis-panel button:focus,
#agent-hustle-selection-tooltip:focus,
#agent-hustle-fab:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
}

/* Ensure proper stacking context */
#agent-hustle-analysis-panel,
#agent-hustle-selection-tooltip,
.agent-hustle-notification,
#agent-hustle-fab {
    position: fixed !important;
    z-index: 2147483647 !important; /* Maximum z-index value */
}

/* Structured Analysis Formatting Styles */
.structured-analysis {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.6 !important;
    color: #333 !important;
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-size: 14px !important;
    border: none !important;
}

/* Section Headers */
.analysis-summary,
.analysis-insights,
.analysis-recommendations,
.analysis-market,
.analysis-section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 12px 16px !important;
    border-radius: 6px !important;
    margin: 16px 0 12px 0 !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.analysis-summary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.analysis-insights {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}

.analysis-recommendations {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
}

.analysis-market {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    color: #333 !important;
}

/* Analysis Points */
.analysis-point {
    margin: 12px 0 !important;
    padding: 12px !important;
    background: #f8f9fa !important;
    border-left: 4px solid #667eea !important;
    border-radius: 0 6px 6px 0 !important;
}

.point-number {
    font-weight: 700 !important;
    color: #667eea !important;
    margin-right: 8px !important;
}

.point-title {
    font-weight: 600 !important;
    color: #333 !important;
}

/* Bullet Points */
.analysis-bullet {
    margin: 8px 0 8px 16px !important;
    padding: 4px 0 !important;
    color: #555 !important;
    position: relative !important;
}

.analysis-bullet::before {
    content: "•" !important;
    color: #667eea !important;
    font-weight: bold !important;
    position: absolute !important;
    left: -16px !important;
}

/* Metrics and Numbers */
.metric-percentage {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
}

.metric-currency {
    background: linear-gradient(135deg, #11998e, #38ef7d) !important;
    color: white !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
}

.metric-number {
    background: #e3f2fd !important;
    color: #1976d2 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    border: 1px solid #bbdefb !important;
}

.metric-time {
    background: #fff3e0 !important;
    color: #f57c00 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    border: 1px solid #ffcc02 !important;
}

.metric-rank {
    background: #f3e5f5 !important;
    color: #7b1fa2 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-weight: 600 !important;
    font-size: 13px !important;
    border: 1px solid #ce93d8 !important;
}

/* Status Indicators */
.status-positive {
    color: #2e7d32 !important;
    font-weight: 600 !important;
    background: #e8f5e8 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    border: 1px solid #a5d6a7 !important;
}

.status-warning {
    color: #f57c00 !important;
    font-weight: 600 !important;
    background: #fff8e1 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    border: 1px solid #ffcc02 !important;
}

.status-negative {
    color: #d32f2f !important;
    font-weight: 600 !important;
    background: #ffebee !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    border: 1px solid #ef9a9a !important;
}

/* Trend Indicators */
.trend-up {
    color: #2e7d32 !important;
    font-weight: 600 !important;
    background: #e8f5e8 !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #a5d6a7 !important;
    display: inline-block !important;
    margin: 0 4px !important;
}

.trend-down {
    color: #d32f2f !important;
    font-weight: 600 !important;
    background: #ffebee !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    border: 1px solid #ef9a9a !important;
    display: inline-block !important;
    margin: 0 4px !important;
}

/* Text Formatting */
.structured-analysis strong {
    color: #333 !important;
    font-weight: 600 !important;
}

.structured-analysis em {
    color: #555 !important;
    font-style: italic !important;
}

.structured-analysis code {
    background: #f8f9fa !important;
    color: #e83e8c !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace !important;
    font-size: 12px !important;
    border: 1px solid #e9ecef !important;
}

/* Spacing and Layout */
.structured-analysis > div {
    margin-bottom: 8px !important;
}

.structured-analysis p {
    margin: 8px 0 !important;
    line-height: 1.6 !important;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .structured-analysis {
        background: #1a1a1a !important;
        color: #e0e0e0 !important;
    }
    
    .analysis-point {
        background: #2a2a2a !important;
        color: #e0e0e0 !important;
    }
    
    .point-title {
        color: #e0e0e0 !important;
    }
    
    .analysis-bullet {
        color: #ccc !important;
    }
    
    .structured-analysis strong {
        color: #e0e0e0 !important;
    }
    
    .structured-analysis em {
        color: #ccc !important;
    }
} 