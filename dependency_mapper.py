#!/usr/bin/env python3
"""
Dependency Mapper Script
Parses all JS files in a directory to map their import/export dependencies.
This helps in understanding the codebase's wiring before a refactor.
"""
import esprima
import json
import os
import sys
from pathlib import Path

def analyze_js_file(file_path):
    """Parses a JS file to find its imports and exports."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    try:
        # Use esprima to parse the JavaScript module
        tree = esprima.parseModule(content, {'loc': True})
    except Exception as e:
        # Return an error if parsing fails
        return {'error': f"Could not parse {file_path}: {e}"}

    imports = []
    exports = []

    for node in tree.body:
        # Find 'import' statements (e.g., import { a, b } from './c')
        if node.type == 'ImportDeclaration':
            source = node.source.value
            specifiers = []
            if node.specifiers:
                for spec in node.specifiers:
                    # Distinguish between named imports (ImportSpecifier) and default/namespace imports
                    if spec.type == 'ImportSpecifier':
                        specifiers.append(spec.imported.name)
                    elif spec.type in ['ImportDefaultSpecifier', 'ImportNamespaceSpecifier']:
                         specifiers.append(spec.local.name)
            imports.append({'source': source, 'specifiers': specifiers})

        # Find 'export' statements
        if node.type == 'ExportNamedDeclaration':
            if node.declaration:
                # Handles declarations like 'export function myFunction() {}' or 'export const myVar = ...'
                if hasattr(node.declaration, 'id') and node.declaration.id:
                    exports.append(node.declaration.id.name)
                elif hasattr(node.declaration, 'declarations'):
                    for decl in node.declaration.declarations:
                        exports.append(decl.id.name)
            elif node.specifiers:
                # Handles statements like 'export { myFunction, myVar }'
                for spec in node.specifiers:
                    exports.append(spec.local.name)
        
        # Handle 'export default'
        if node.type == 'ExportDefaultDeclaration':
            exports.append('default')

    return {'imports': imports, 'exports': exports}

def generate_dependency_report(directory):
    """Analyzes all JS files in a directory and creates a dependency report."""
    report = {}
    # Recursively find all .js files in the target directory
    js_files = list(Path(directory).rglob('*.js'))

    print(f"Found {len(js_files)} JavaScript files to analyze...")

    for file_path in js_files:
        # Use relative paths for cleaner report keys
        relative_path = os.path.relpath(file_path, directory).replace('\\', '/')
        report[relative_path] = analyze_js_file(file_path)

    return report

if __name__ == "__main__":
    try:
        import esprima
    except ImportError:
        print("Error: The 'esprima-python' library is not installed.", file=sys.stderr)
        print("Please install it by running the following command:", file=sys.stderr)
        print("pip install esprima-python", file=sys.stderr)
        sys.exit(1)

    if len(sys.argv) < 2:
        print("Usage: python dependency_mapper.py <directory_to_scan>", file=sys.stderr)
        sys.exit(1)

    target_directory = sys.argv[1]
    if not os.path.isdir(target_directory):
        print(f"Error: Directory not found at '{target_directory}'", file=sys.stderr)
        sys.exit(1)
        
    dependency_map = generate_dependency_report(target_directory)

    output_file = 'dependency_report.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dependency_map, f, indent=2)
    
    print(f"\n✅ Dependency map generated successfully!")
    print(f"📋 Report saved to: {output_file}") 