/**
 * Chat Stream Handler
 * Handles real-time streaming responses and tool execution visibility
 */
export class ChatStreamHandler {
    constructor(chatManager) {
        this.chatManager = chatManager;
        this.currentMessage = null;
        this.isStreaming = false;
        this.streamBuffer = '';
        this.toolExecutions = new Map();
    }

    /**
     * Start streaming for a new message
     */
    startStreaming(messageId) {
        this.currentMessage = {
            id: messageId,
            role: 'assistant',
            content: '',
            timestamp: new Date().toISOString(),
            tools: []
        };
        this.isStreaming = true;
        this.streamBuffer = '';
        
        // Create the streaming message element in UI
        this.createStreamingMessageElement();
    }

    /**
     * Handle a streaming chunk from the response
     */
    async handleStreamChunk(chunk) {
        if (!this.isStreaming || !this.currentMessage) {
            return;
        }

        try {
            // PATTERN: Parse existing streaming format from background.js
            // API returns custom format: "0:\"Hello \"" for streaming chunks
            if (chunk.startsWith('0:')) {
                const content = JSON.parse(chunk.substring(2));
                await this.appendContentToMessage(content);
            }
            // Handle tool execution notifications
            else if (chunk.includes('tool_call:')) {
                await this.handleToolExecution(chunk);
            }
            // Handle tool results
            else if (chunk.includes('tool_result:')) {
                await this.handleToolResult(chunk);
            }
            // Handle error responses
            else if (chunk.includes('error:')) {
                await this.handleStreamError(chunk);
            }
            // Handle completion
            else if (chunk.includes('done')) {
                await this.completeStreaming();
            }
        } catch (error) {
            console.error('Error handling stream chunk:', error);
            this.handleStreamError(`Error processing chunk: ${error.message}`);
        }
    }

    /**
     * Process streaming response text line by line
     */
    async processStreamingResponse(responseText) {
        if (!responseText) return;

        // Split response into lines and process each
        const lines = responseText.split('\n');
        
        for (const line of lines) {
            if (line.trim()) {
                await this.handleStreamChunk(line.trim());
            }
        }
    }

    /**
     * Append content to the current streaming message
     */
    async appendContentToMessage(content) {
        if (!this.currentMessage) return;

        this.currentMessage.content += content;
        this.updateStreamingMessageUI(content);
        
        // Auto-scroll if enabled
        if (this.chatManager.settings.autoScroll) {
            this.autoScroll();
        }
    }

    /**
     * Handle tool execution notification
     */
    async handleToolExecution(chunk) {
        try {
            // Parse tool execution data
            const toolData = this.parseToolData(chunk);
            if (!toolData) return;

            const toolExecution = {
                id: toolData.id || `tool-${Date.now()}`,
                name: toolData.name || 'Unknown Tool',
                status: 'pending',
                startTime: new Date().toISOString(),
                arguments: toolData.arguments
            };

            // Add to current message tools
            this.currentMessage.tools.push(toolExecution);
            this.toolExecutions.set(toolExecution.id, toolExecution);

            // Show tool execution in UI if enabled
            if (this.chatManager.settings.showToolExecution) {
                this.showToolInProgress(toolExecution);
            }
        } catch (error) {
            console.error('Error handling tool execution:', error);
        }
    }

    /**
     * Handle tool execution result
     */
    async handleToolResult(chunk) {
        try {
            const resultData = this.parseToolResult(chunk);
            if (!resultData || !resultData.id) return;

            const toolExecution = this.toolExecutions.get(resultData.id);
            if (!toolExecution) return;

            // Update tool execution status
            toolExecution.status = resultData.success ? 'success' : 'error';
            toolExecution.endTime = new Date().toISOString();
            toolExecution.result = resultData.result;
            toolExecution.error = resultData.error;

            // Update UI
            if (this.chatManager.settings.showToolExecution) {
                this.updateToolExecutionUI(toolExecution);
            }
        } catch (error) {
            console.error('Error handling tool result:', error);
        }
    }

    /**
     * Handle streaming errors
     */
    async handleStreamError(errorChunk) {
        console.error('Streaming error:', errorChunk);
        
        if (this.currentMessage) {
            this.currentMessage.content += '\n\n⚠️ Error occurred during response generation.';
            this.updateStreamingMessageUI('\n\n⚠️ Error occurred during response generation.');
        }
        
        this.completeStreaming(true);
    }

    /**
     * Complete the streaming process
     */
    async completeStreaming(hasError = false) {
        if (!this.isStreaming || !this.currentMessage) return;

        this.isStreaming = false;

        // Add completed message to chat session
        if (this.chatManager.currentSession) {
            this.chatManager.currentSession.messages.push(this.currentMessage);
            this.chatManager.currentSession.updatedAt = new Date().toISOString();
            
            // Update session title if it's the first assistant message
            if (this.chatManager.currentSession.messages.filter(m => m.role === 'assistant').length === 1) {
                this.generateSessionTitle();
            }
        }

        // Finalize UI
        this.finalizeStreamingMessageUI();
        
        // Save to storage
        await this.chatManager.saveChatHistory();
        
        // Clear current message
        this.currentMessage = null;
        
        console.log('Streaming completed', hasError ? 'with errors' : 'successfully');
    }

    /**
     * Create the streaming message element in the UI
     */
    createStreamingMessageElement() {
        if (!this.currentMessage) return;

        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageElement = document.createElement('div');
        messageElement.className = 'message assistant streaming';
        messageElement.dataset.messageId = this.currentMessage.id;

        const time = new Date(this.currentMessage.timestamp).toLocaleTimeString();
        
        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-role">Agent Hustle</span>
                <span class="message-time">${time}</span>
                <span class="streaming-indicator">●</span>
            </div>
            <div class="message-content"></div>
            <div class="tool-executions"></div>
        `;

        chatMessages.appendChild(messageElement);
        this.autoScroll();
    }

    /**
     * Update the streaming message UI with new content
     */
    updateStreamingMessageUI(newContent) {
        const messageElement = document.querySelector(`.message[data-message-id="${this.currentMessage.id}"]`);
        if (!messageElement) return;

        const contentElement = messageElement.querySelector('.message-content');
        if (contentElement) {
            contentElement.innerHTML = this.formatMessageContent(this.currentMessage.content);
        }
    }

    /**
     * Finalize the streaming message UI
     */
    finalizeStreamingMessageUI() {
        if (!this.currentMessage) return;

        const messageElement = document.querySelector(`.message[data-message-id="${this.currentMessage.id}"]`);
        if (!messageElement) return;

        // Remove streaming class and indicator
        messageElement.classList.remove('streaming');
        const streamingIndicator = messageElement.querySelector('.streaming-indicator');
        if (streamingIndicator) {
            streamingIndicator.remove();
        }
    }

    /**
     * Show tool execution in progress
     */
    showToolInProgress(toolExecution) {
        const messageElement = document.querySelector(`.message[data-message-id="${this.currentMessage.id}"]`);
        if (!messageElement) return;

        const toolExecutionsElement = messageElement.querySelector('.tool-executions');
        if (!toolExecutionsElement) return;

        const toolElement = document.createElement('div');
        toolElement.className = 'tool-execution pending';
        toolElement.dataset.toolId = toolExecution.id;
        toolElement.innerHTML = `
            <div class="tool-header">
                <span class="tool-name">${toolExecution.name}</span>
                <span class="tool-status">🔄 Running...</span>
            </div>
            <div class="tool-details" style="display: none;">
                <div class="tool-arguments">${JSON.stringify(toolExecution.arguments, null, 2)}</div>
            </div>
        `;

        toolExecutionsElement.appendChild(toolElement);
        this.autoScroll();
    }

    /**
     * Update tool execution UI with results
     */
    updateToolExecutionUI(toolExecution) {
        const toolElement = document.querySelector(`.tool-execution[data-tool-id="${toolExecution.id}"]`);
        if (!toolElement) return;

        toolElement.className = `tool-execution ${toolExecution.status}`;
        
        const statusElement = toolElement.querySelector('.tool-status');
        if (statusElement) {
            if (toolExecution.status === 'success') {
                statusElement.innerHTML = '✅ Completed';
            } else if (toolExecution.status === 'error') {
                statusElement.innerHTML = '❌ Failed';
            }
        }

        // Add result if available
        if (toolExecution.result || toolExecution.error) {
            const detailsElement = toolElement.querySelector('.tool-details');
            if (detailsElement) {
                const resultContent = toolExecution.result ? 
                    `<div class="tool-result">${JSON.stringify(toolExecution.result, null, 2)}</div>` : 
                    `<div class="tool-error">${toolExecution.error}</div>`;
                detailsElement.innerHTML += resultContent;
            }
        }
    }

    /**
     * Parse tool data from chunk
     */
    parseToolData(chunk) {
        try {
            // Expected format: "tool_call:{json_data}"
            const jsonStart = chunk.indexOf('{');
            if (jsonStart === -1) return null;
            
            const jsonStr = chunk.substring(jsonStart);
            return JSON.parse(jsonStr);
        } catch (error) {
            console.error('Error parsing tool data:', error);
            return null;
        }
    }

    /**
     * Parse tool result from chunk
     */
    parseToolResult(chunk) {
        try {
            // Expected format: "tool_result:{json_data}"
            const jsonStart = chunk.indexOf('{');
            if (jsonStart === -1) return null;
            
            const jsonStr = chunk.substring(jsonStart);
            return JSON.parse(jsonStr);
        } catch (error) {
            console.error('Error parsing tool result:', error);
            return null;
        }
    }

    /**
     * Format message content for display
     */
    formatMessageContent(content) {
        if (!content) return '';
        
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code class="inline-code">$1</code>')
            .replace(/```([\s\S]*?)```/g, '<pre><code class="code-block">$1</code></pre>')
            .replace(/\n/g, '<br>');
    }

    /**
     * Auto-scroll to bottom of chat
     */
    autoScroll() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages && this.chatManager.settings.autoScroll) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    /**
     * Generate session title based on first messages
     */
    generateSessionTitle() {
        if (!this.chatManager.currentSession || this.chatManager.currentSession.messages.length < 2) {
            return;
        }

        const firstUserMessage = this.chatManager.currentSession.messages.find(m => m.role === 'user');
        if (firstUserMessage && firstUserMessage.content) {
            // Generate title from first user message (max 50 chars)
            let title = firstUserMessage.content.substring(0, 50);
            if (firstUserMessage.content.length > 50) {
                title += '...';
            }
            this.chatManager.currentSession.title = title;
        }
    }

    /**
     * Cancel streaming
     */
    cancelStreaming() {
        if (this.isStreaming) {
            this.handleStreamError('Streaming cancelled by user');
        }
    }

    /**
     * Check if currently streaming
     */
    isCurrentlyStreaming() {
        return this.isStreaming;
    }

    /**
     * Get current streaming message
     */
    getCurrentStreamingMessage() {
        return this.currentMessage;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.isStreaming = false;
        this.currentMessage = null;
        this.streamBuffer = '';
        this.toolExecutions.clear();
    }
}