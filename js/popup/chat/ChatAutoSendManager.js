/**
 * Chat Auto Send Manager
 * Handles automatic sending of analysis results to chat sessions
 */
import { BaseManager } from '../core/BaseManager.js';
import { checkProStatus } from '../../user/proStatus.js';

export class ChatAutoSendManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.storageKeys = {
            chatAutoSendSettings: 'agent_hustle_chat_auto_send_settings'
        };
        this.maxFailures = 3; // Auto-disable after 3 failures
    }

    /**
     * Initialize the chat auto-send manager
     */
    async init() {
        await super.init();
        await this.initializeSettings();
        console.log('ChatAutoSendManager initialized');
    }

    /**
     * Initialize default settings if not present
     */
    async initializeSettings() {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatAutoSendSettings]);
            if (!result[this.storageKeys.chatAutoSendSettings]) {
                const defaultSettings = {
                    enabled: false,
                    chatSessionId: null,
                    createNewSession: true,
                    failureCount: 0,
                    lastSent: null,
                    autoDisabled: false
                };
                await chrome.storage.local.set({
                    [this.storageKeys.chatAutoSendSettings]: defaultSettings
                });
            }
        } catch (error) {
            this.handleError(error, 'Initializing auto-send settings');
        }
    }

    /**
     * Handle auto-send to chat after successful analysis
     * @param {Object} analysisData - The analysis result data
     * @returns {Promise<void>}
     */
    async handleAutoSendToChat(analysisData) {
        try {
            // CRITICAL: Always validate Pro status first
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                return; // Auto-send is Pro-only
            }

            // Get auto-send settings
            const settings = await this.getChatAutoSendSettings();
            if (!settings.enabled || settings.autoDisabled) {
                return; // Auto-send disabled or auto-disabled due to failures
            }

            // Find or create target chat session
            const targetSession = await this.findOrCreateTargetSession(settings);
            if (!targetSession) {
                await this.handleAutoSendFailure('Failed to create target chat session');
                return;
            }

            // Format analysis data as chat message
            const message = this.formatAnalysisAsMessage(analysisData);
            
            // Add message to session and save
            targetSession.messages.push(message);
            targetSession.updatedAt = new Date().toISOString();
            
            // Save updated session
            const dataManager = this.getManager('dataManager');
            await dataManager.saveChatSession(targetSession);

            // Update success tracking
            await this.updateAutoSendSuccess();

        } catch (error) {
            this.handleError(error, 'Auto-send to chat');
            await this.handleAutoSendFailure(error.message);
        }
    }

    /**
     * Find existing or create new target chat session
     * @param {Object} settings - Auto-send settings
     * @returns {Promise<Object>} The target chat session
     */
    async findOrCreateTargetSession(settings) {
        try {
            const dataManager = this.getManager('dataManager');
            
            // Try to find existing session if specified
            if (settings.chatSessionId) {
                const existingSession = await this.loadChatSession(settings.chatSessionId);
                if (existingSession) {
                    return existingSession;
                }
            }

            // Create new session if enabled or if existing session not found
            if (settings.createNewSession) {
                const newSession = {
                    id: `chat-${Date.now()}`,
                    title: 'Auto-Send Analysis',
                    customTitle: false,
                    autoGenerated: true,
                    messages: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    isPro: true
                };
                
                // Save the new session
                await dataManager.saveChatSession(newSession);
                
                // Update settings to use this new session
                await this.updateChatAutoSendSettings({
                    ...settings,
                    chatSessionId: newSession.id
                });
                
                return newSession;
            }

            return null;

        } catch (error) {
            this.handleError(error, 'Finding or creating target session');
            throw error;
        }
    }

    /**
     * Format analysis data as chat message
     * @param {Object} analysisData - The analysis result data
     * @returns {Object} Formatted chat message
     */
    formatAnalysisAsMessage(analysisData) {
        const timestamp = new Date().toISOString();
        
        let content = '📊 **Analysis Result**\n\n';
        
        if (analysisData.type) {
            content += `**Type:** ${analysisData.type}\n`;
        }
        
        if (analysisData.url) {
            content += `**URL:** ${analysisData.url}\n`;
        }
        
        if (analysisData.result) {
            content += `\n**Result:**\n${analysisData.result}`;
        }
        
        if (analysisData.summary) {
            content += `\n\n**Summary:**\n${analysisData.summary}`;
        }

        return {
            id: `auto-send-${Date.now()}`,
            content: content,
            sender: 'system',
            timestamp: timestamp,
            type: 'analysis-result',
            autoSent: true
        };
    }

    /**
     * Get chat auto-send settings
     * @returns {Promise<Object>} Auto-send settings
     */
    async getChatAutoSendSettings() {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatAutoSendSettings]);
            return result[this.storageKeys.chatAutoSendSettings] || {
                enabled: false,
                chatSessionId: null,
                createNewSession: true,
                failureCount: 0,
                lastSent: null,
                autoDisabled: false
            };
        } catch (error) {
            this.handleError(error, 'Loading chat auto-send settings');
            return {};
        }
    }

    /**
     * Update chat auto-send settings
     * @param {Object} settings - Updated settings
     * @returns {Promise<void>}
     */
    async updateChatAutoSendSettings(settings) {
        try {
            await chrome.storage.local.set({
                [this.storageKeys.chatAutoSendSettings]: settings
            });
        } catch (error) {
            this.handleError(error, 'Updating chat auto-send settings');
            throw error;
        }
    }

    /**
     * Handle auto-send failure
     * @param {string} errorMessage - The error message
     * @returns {Promise<void>}
     */
    async handleAutoSendFailure(errorMessage) {
        try {
            const settings = await this.getChatAutoSendSettings();
            const updatedSettings = {
                ...settings,
                failureCount: settings.failureCount + 1,
                lastError: errorMessage,
                lastFailure: new Date().toISOString()
            };

            // Auto-disable if failure count exceeds threshold
            if (updatedSettings.failureCount >= this.maxFailures) {
                updatedSettings.autoDisabled = true;
                updatedSettings.enabled = false;
            }

            await this.updateChatAutoSendSettings(updatedSettings);

        } catch (error) {
            this.handleError(error, 'Handling auto-send failure');
        }
    }

    /**
     * Update auto-send success tracking
     * @returns {Promise<void>}
     */
    async updateAutoSendSuccess() {
        try {
            const settings = await this.getChatAutoSendSettings();
            const updatedSettings = {
                ...settings,
                failureCount: 0, // Reset failure count on success
                lastSent: new Date().toISOString(),
                autoDisabled: false // Re-enable if it was auto-disabled
            };

            await this.updateChatAutoSendSettings(updatedSettings);

        } catch (error) {
            this.handleError(error, 'Updating auto-send success');
        }
    }

    /**
     * Toggle auto-send enabled state
     * @param {boolean} enabled - Whether to enable auto-send
     * @returns {Promise<void>}
     */
    async toggleAutoSend(enabled) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                throw new Error('Pro subscription required for auto-send to chat');
            }

            const settings = await this.getChatAutoSendSettings();
            const updatedSettings = {
                ...settings,
                enabled: enabled,
                autoDisabled: false // Clear auto-disabled state when manually toggling
            };

            await this.updateChatAutoSendSettings(updatedSettings);
            
            const message = enabled ? 'Auto-send to chat enabled' : 'Auto-send to chat disabled';
            this.handleSuccess(message);

        } catch (error) {
            this.handleError(error, 'Toggling auto-send');
            throw error;
        }
    }

    /**
     * Set target chat session
     * @param {string} sessionId - The chat session ID to target
     * @returns {Promise<void>}
     */
    async setTargetSession(sessionId) {
        try {
            const settings = await this.getChatAutoSendSettings();
            const updatedSettings = {
                ...settings,
                chatSessionId: sessionId
            };

            await this.updateChatAutoSendSettings(updatedSettings);
            this.handleSuccess('Target chat session updated');

        } catch (error) {
            this.handleError(error, 'Setting target session');
            throw error;
        }
    }

    /**
     * Load a specific chat session by ID
     * @param {string} sessionId - The session ID to load
     * @returns {Promise<Object|null>} The chat session or null if not found
     */
    async loadChatSession(sessionId) {
        try {
            const data = await chrome.storage.local.get(['agent_hustle_chat_history']);
            const sessions = data.agent_hustle_chat_history || [];
            
            return sessions.find(session => session.id === sessionId) || null;
            
        } catch (error) {
            this.handleError(error, 'Loading chat session');
            return null;
        }
    }

    /**
     * Get available chat sessions for selection
     * @returns {Promise<Array>} Array of chat sessions
     */
    async getAvailableChatSessions() {
        try {
            const data = await chrome.storage.local.get(['agent_hustle_chat_history']);
            const sessions = data.agent_hustle_chat_history || [];
            
            // Return simplified session info for dropdown
            return sessions.map(session => ({
                id: session.id,
                title: session.title || 'Untitled Chat',
                createdAt: session.createdAt,
                messageCount: session.messages ? session.messages.length : 0
            }));

        } catch (error) {
            this.handleError(error, 'Loading available chat sessions');
            return [];
        }
    }
}