/**
 * Chat Storage Manager
 * Handles chat history persistence, pagination, and storage operations
 */
import { BaseManager } from '../core/BaseManager.js';

export class ChatStorageManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.maxHistorySize = 100; // Maximum number of chat sessions to keep
        this.maxMessagesPerPage = 20; // For pagination
        this.storageKeys = {
            chatHistory: 'agent_hustle_chat_history',
            chatSettings: 'agent_hustle_chat_settings'
        };
    }

    /**
     * Initialize the storage manager
     */
    async init() {
        await super.init();
        await this.initializeStorage();
        console.log('ChatStorageManager initialized');
    }

    /**
     * Initialize storage structure if needed
     */
    async initializeStorage() {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatHistory]);
            if (!result[this.storageKeys.chatHistory]) {
                await chrome.storage.local.set({
                    [this.storageKeys.chatHistory]: []
                });
            }
        } catch (error) {
            this.handleError(error, 'Initializing chat storage');
        }
    }

    /**
     * Save a chat session to storage
     */
    async saveChat(chatSession) {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatHistory]);
            let chatHistory = result[this.storageKeys.chatHistory] || [];

            // Find existing session or add new one
            const existingIndex = chatHistory.findIndex(session => session.id === chatSession.id);
            if (existingIndex !== -1) {
                // Update existing session
                chatHistory[existingIndex] = chatSession;
            } else {
                // Add new session at the beginning
                chatHistory.unshift(chatSession);
            }

            // Enforce maximum history size
            if (chatHistory.length > this.maxHistorySize) {
                chatHistory = chatHistory.slice(0, this.maxHistorySize);
            }

            await chrome.storage.local.set({
                [this.storageKeys.chatHistory]: chatHistory
            });

            return true;
        } catch (error) {
            this.handleError(error, 'Saving chat session');
            return false;
        }
    }

    /**
     * Load all chat sessions from storage
     */
    async loadChats() {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatHistory]);
            const chatHistory = result[this.storageKeys.chatHistory] || [];
            
            // Sort by updatedAt (newest first)
            return chatHistory.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
        } catch (error) {
            this.handleError(error, 'Loading chat sessions');
            return [];
        }
    }

    /**
     * Load chats with pagination support
     */
    async loadChatsWithPagination(page = 1, itemsPerPage = 10) {
        try {
            const allChats = await this.loadChats();
            const startIndex = (page - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            
            const paginatedChats = allChats.slice(startIndex, endIndex);
            
            return {
                data: paginatedChats,
                pagination: {
                    currentPage: page,
                    itemsPerPage: itemsPerPage,
                    totalItems: allChats.length,
                    totalPages: Math.ceil(allChats.length / itemsPerPage),
                    hasNextPage: endIndex < allChats.length,
                    hasPreviousPage: page > 1
                }
            };
        } catch (error) {
            this.handleError(error, 'Loading paginated chats');
            return {
                data: [],
                pagination: {
                    currentPage: 1,
                    itemsPerPage: itemsPerPage,
                    totalItems: 0,
                    totalPages: 0,
                    hasNextPage: false,
                    hasPreviousPage: false
                }
            };
        }
    }

    /**
     * Load a specific chat session by ID
     */
    async loadChatById(chatId) {
        try {
            const chatHistory = await this.loadChats();
            return chatHistory.find(session => session.id === chatId) || null;
        } catch (error) {
            this.handleError(error, 'Loading chat by ID');
            return null;
        }
    }

    /**
     * Delete a chat session by ID
     */
    async deleteChat(chatId) {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatHistory]);
            let chatHistory = result[this.storageKeys.chatHistory] || [];

            // Filter out the chat to delete
            chatHistory = chatHistory.filter(session => session.id !== chatId);

            await chrome.storage.local.set({
                [this.storageKeys.chatHistory]: chatHistory
            });

            return true;
        } catch (error) {
            this.handleError(error, 'Deleting chat session');
            return false;
        }
    }

    /**
     * Delete multiple chat sessions by IDs
     */
    async deleteMultipleChats(chatIds) {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatHistory]);
            let chatHistory = result[this.storageKeys.chatHistory] || [];

            // Filter out the chats to delete
            chatHistory = chatHistory.filter(session => !chatIds.includes(session.id));

            await chrome.storage.local.set({
                [this.storageKeys.chatHistory]: chatHistory
            });

            return true;
        } catch (error) {
            this.handleError(error, 'Deleting multiple chat sessions');
            return false;
        }
    }

    /**
     * Clear all chat history
     */
    async clearAllChats() {
        try {
            await chrome.storage.local.set({
                [this.storageKeys.chatHistory]: []
            });
            return true;
        } catch (error) {
            this.handleError(error, 'Clearing all chat history');
            return false;
        }
    }

    /**
     * Search chat sessions by content
     */
    async searchChats(query, limit = 10) {
        try {
            const allChats = await this.loadChats();
            const searchQuery = query.toLowerCase();
            
            const matchingChats = allChats.filter(session => {
                // Search in session title
                if (session.title && session.title.toLowerCase().includes(searchQuery)) {
                    return true;
                }
                
                // Search in message content
                return session.messages.some(message => 
                    message.content && message.content.toLowerCase().includes(searchQuery)
                );
            });

            return matchingChats.slice(0, limit);
        } catch (error) {
            this.handleError(error, 'Searching chats');
            return [];
        }
    }

    /**
     * Get chat statistics
     */
    async getChatStats() {
        try {
            const allChats = await this.loadChats();
            const totalMessages = allChats.reduce((sum, session) => sum + session.messages.length, 0);
            const totalUserMessages = allChats.reduce((sum, session) => 
                sum + session.messages.filter(msg => msg.role === 'user').length, 0);
            const totalAssistantMessages = allChats.reduce((sum, session) => 
                sum + session.messages.filter(msg => msg.role === 'assistant').length, 0);

            return {
                totalSessions: allChats.length,
                totalMessages: totalMessages,
                totalUserMessages: totalUserMessages,
                totalAssistantMessages: totalAssistantMessages,
                averageMessagesPerSession: allChats.length > 0 ? Math.round(totalMessages / allChats.length) : 0,
                oldestSession: allChats.length > 0 ? allChats[allChats.length - 1].createdAt : null,
                newestSession: allChats.length > 0 ? allChats[0].createdAt : null
            };
        } catch (error) {
            this.handleError(error, 'Getting chat statistics');
            return {
                totalSessions: 0,
                totalMessages: 0,
                totalUserMessages: 0,
                totalAssistantMessages: 0,
                averageMessagesPerSession: 0,
                oldestSession: null,
                newestSession: null
            };
        }
    }

    /**
     * Export chat history to JSON
     */
    async exportChatHistory() {
        try {
            const allChats = await this.loadChats();
            const stats = await this.getChatStats();
            
            const exportData = {
                exportDate: new Date().toISOString(),
                version: '1.0',
                statistics: stats,
                chats: allChats
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-chat-history-${Date.now()}.json`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            return true;
        } catch (error) {
            this.handleError(error, 'Exporting chat history');
            return false;
        }
    }

    /**
     * Import chat history from JSON
     */
    async importChatHistory(file) {
        try {
            const text = await file.text();
            const importData = JSON.parse(text);
            
            if (!importData.chats || !Array.isArray(importData.chats)) {
                throw new Error('Invalid import file format');
            }

            // Validate chat structure
            const validChats = importData.chats.filter(chat => 
                chat.id && chat.messages && Array.isArray(chat.messages)
            );

            if (validChats.length === 0) {
                throw new Error('No valid chat sessions found in import file');
            }

            // Merge with existing chats
            const existingChats = await this.loadChats();
            const existingIds = new Set(existingChats.map(chat => chat.id));
            
            // Only add chats that don't already exist
            const newChats = validChats.filter(chat => !existingIds.has(chat.id));
            
            if (newChats.length > 0) {
                const mergedChats = [...existingChats, ...newChats];
                await chrome.storage.local.set({
                    [this.storageKeys.chatHistory]: mergedChats
                });
            }

            return {
                success: true,
                imported: newChats.length,
                duplicates: validChats.length - newChats.length
            };
        } catch (error) {
            this.handleError(error, 'Importing chat history');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Save chat settings
     */
    async saveChatSettings(settings) {
        try {
            await chrome.storage.local.set({
                [this.storageKeys.chatSettings]: settings
            });
            return true;
        } catch (error) {
            this.handleError(error, 'Saving chat settings');
            return false;
        }
    }

    /**
     * Load chat settings
     */
    async loadChatSettings() {
        try {
            const result = await chrome.storage.local.get([this.storageKeys.chatSettings]);
            return result[this.storageKeys.chatSettings] || {
                maxHistorySize: 100,
                autoScroll: true,
                showToolExecution: true,
                showTimestamps: true,
                theme: 'default'
            };
        } catch (error) {
            this.handleError(error, 'Loading chat settings');
            return {
                maxHistorySize: 100,
                autoScroll: true,
                showToolExecution: true,
                showTimestamps: true,
                theme: 'default'
            };
        }
    }

    /**
     * Get storage usage information
     */
    async getStorageUsage() {
        try {
            const result = await chrome.storage.local.getBytesInUse([this.storageKeys.chatHistory]);
            const totalBytes = result || 0;
            const maxBytes = chrome.storage.local.QUOTA_BYTES || 10485760; // 10MB default
            
            return {
                usedBytes: totalBytes,
                maxBytes: maxBytes,
                usedPercentage: Math.round((totalBytes / maxBytes) * 100),
                remainingBytes: maxBytes - totalBytes
            };
        } catch (error) {
            this.handleError(error, 'Getting storage usage');
            return {
                usedBytes: 0,
                maxBytes: 10485760,
                usedPercentage: 0,
                remainingBytes: 10485760
            };
        }
    }

    /**
     * Cleanup old chat sessions if storage is getting full
     */
    async cleanupOldChats() {
        try {
            const usage = await this.getStorageUsage();
            
            // If storage is more than 80% full, remove oldest sessions
            if (usage.usedPercentage > 80) {
                const allChats = await this.loadChats();
                const keepCount = Math.floor(this.maxHistorySize * 0.7); // Keep 70% of max
                
                if (allChats.length > keepCount) {
                    const chatsToKeep = allChats.slice(0, keepCount);
                    await chrome.storage.local.set({
                        [this.storageKeys.chatHistory]: chatsToKeep
                    });
                    
                    return {
                        cleaned: true,
                        removed: allChats.length - keepCount
                    };
                }
            }
            
            return { cleaned: false, removed: 0 };
        } catch (error) {
            this.handleError(error, 'Cleaning up old chats');
            return { cleaned: false, removed: 0 };
        }
    }
}