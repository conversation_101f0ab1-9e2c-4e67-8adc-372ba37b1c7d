/**
 * Chat Naming Manager
 * Handles chat session naming and title management with Pro feature gating
 */
import { BaseManager } from '../core/BaseManager.js';
import { checkProStatus } from '../../user/proStatus.js';

export class ChatNamingManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.maxTitleLength = 50;
        this.storageKeys = {
            chatHistory: 'agent_hustle_chat_history'
        };
    }

    /**
     * Initialize the chat naming manager
     */
    async init() {
        await super.init();
        console.log('ChatNamingManager initialized');
    }

    /**
     * Rename a chat session
     * @param {string} sessionId - The session ID to rename
     * @param {string} newTitle - The new title for the session
     * @returns {Promise<Object>} The updated session
     */
    async renameChat(sessionId, newTitle) {
        try {
            // CRITICAL: Always validate Pro status first
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                throw new Error('Pro subscription required for chat naming');
            }

            // Validate input
            if (!sessionId || typeof sessionId !== 'string') {
                throw new Error('Invalid session ID');
            }

            const validatedTitle = this.validateChatTitle(newTitle);
            
            // Get session from storage
            const session = await this.loadChatSession(sessionId);
            
            if (!session) {
                throw new Error('Chat session not found');
            }

            // Update session with custom title
            session.title = validatedTitle;
            session.customTitle = true;
            session.updatedAt = new Date().toISOString();

            // Save updated session
            const dataManager = this.getManager('dataManager');
            await dataManager.saveChatSession(session);
            
            this.handleSuccess('Chat renamed successfully');
            return session;

        } catch (error) {
            this.handleError(error, 'Renaming chat');
            throw error;
        }
    }

    /**
     * Validate chat title
     * @param {string} title - The title to validate
     * @returns {string} The validated title
     */
    validateChatTitle(title) {
        if (!title || typeof title !== 'string') {
            throw new Error('Title is required');
        }

        const trimmedTitle = title.trim();
        
        if (trimmedTitle.length === 0) {
            throw new Error('Title cannot be empty');
        }

        if (trimmedTitle.length > this.maxTitleLength) {
            throw new Error(`Title must be ${this.maxTitleLength} characters or less`);
        }

        // Remove any potentially harmful characters
        const cleanTitle = trimmedTitle.replace(/[<>\"'&]/g, '');
        
        if (cleanTitle.length === 0) {
            throw new Error('Title contains invalid characters');
        }

        return cleanTitle;
    }

    /**
     * Generate automatic title from first message
     * @param {string} firstMessage - The first message to generate title from
     * @returns {string} Generated title
     */
    generateAutoTitle(firstMessage) {
        if (!firstMessage || typeof firstMessage !== 'string') {
            return 'New Chat';
        }

        const trimmed = firstMessage.trim();
        if (trimmed.length === 0) {
            return 'New Chat';
        }

        // Take first 30 characters and add ellipsis if needed
        const autoTitle = trimmed.length > 30 ? `${trimmed.substring(0, 30)}...` : trimmed;
        return this.validateChatTitle(autoTitle);
    }

    /**
     * Update chat session title
     * @param {string} sessionId - The session ID
     * @param {string} title - The new title
     * @param {boolean} isCustom - Whether this is a custom user title
     * @returns {Promise<Object>} The updated session
     */
    async updateChatTitle(sessionId, title, isCustom = false) {
        try {
            const session = await this.loadChatSession(sessionId);
            
            if (!session) {
                throw new Error('Chat session not found');
            }

            // Only update if not already custom titled (unless this is a custom update)
            if (!session.customTitle || isCustom) {
                session.title = this.validateChatTitle(title);
                session.customTitle = isCustom;
                session.autoGenerated = !isCustom;
                session.updatedAt = new Date().toISOString();

                const dataManager = this.getManager('dataManager');
                await dataManager.saveChatSession(session);
                return session;
            }

            return session;

        } catch (error) {
            this.handleError(error, 'Updating chat title');
            throw error;
        }
    }

    /**
     * Load a specific chat session by ID
     * @param {string} sessionId - The session ID to load
     * @returns {Promise<Object|null>} The chat session or null if not found
     */
    async loadChatSession(sessionId) {
        try {
            const data = await chrome.storage.local.get(['agent_hustle_chat_history']);
            const sessions = data.agent_hustle_chat_history || [];
            
            return sessions.find(session => session.id === sessionId) || null;
            
        } catch (error) {
            this.handleError(error, 'Loading chat session');
            return null;
        }
    }

    /**
     * Check if user can rename chats (Pro feature)
     * @returns {Promise<boolean>} Whether user can rename chats
     */
    async canRenameChats() {
        try {
            const proStatus = await checkProStatus();
            return proStatus.isPro;
        } catch (error) {
            this.handleError(error, 'Checking rename permissions');
            return false;
        }
    }
}