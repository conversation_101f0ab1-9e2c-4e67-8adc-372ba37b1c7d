/**
 * Pagination Utility Module
 * Provides reusable pagination functionality for different data sources
 */

export class PaginationManager {
    constructor(options = {}) {
        this.itemsPerPage = options.itemsPerPage || 10;
        this.currentPage = 1;
        this.totalItems = 0;
        this.totalPages = 0;
        this.containerId = options.containerId;
        this.onPageChange = options.onPageChange || (() => {});
    }

    /**
     * Calculate pagination data based on total items
     */
    calculatePagination(totalItems) {
        this.totalItems = totalItems;
        this.totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        // Ensure current page is within bounds
        if (this.currentPage > this.totalPages && this.totalPages > 0) {
            this.currentPage = this.totalPages;
        }
        if (this.currentPage < 1) {
            this.currentPage = 1;
        }

        return {
            currentPage: this.currentPage,
            totalPages: this.totalPages,
            totalItems: this.totalItems,
            itemsPerPage: this.itemsPerPage,
            startIndex: (this.currentPage - 1) * this.itemsPerPage,
            endIndex: Math.min(this.currentPage * this.itemsPerPage, this.totalItems)
        };
    }

    /**
     * Get paginated slice of data
     */
    getPaginatedData(data) {
        const pagination = this.calculatePagination(data.length);
        return {
            data: data.slice(pagination.startIndex, pagination.endIndex),
            pagination
        };
    }

    /**
     * Generate pagination controls HTML
     */
    generatePaginationHTML(pagination) {
        if (pagination.totalPages <= 1) {
            return '';
        }

        const { currentPage, totalPages, totalItems, itemsPerPage } = pagination;
        const startItem = (currentPage - 1) * itemsPerPage + 1;
        const endItem = Math.min(currentPage * itemsPerPage, totalItems);

        let html = `
            <div class="pagination-container">
                <div class="pagination-info">
                    Showing ${startItem}-${endItem} of ${totalItems} items
                </div>
                <div class="pagination-controls">
        `;

        // Previous button
        html += `
            <button class="pagination-btn ${currentPage === 1 ? 'disabled' : ''}" 
                    data-page="${currentPage - 1}" 
                    ${currentPage === 1 ? 'disabled' : ''}>
                ‹ Previous
            </button>
        `;

        // Page numbers
        const pageNumbers = this.getPageNumbers(currentPage, totalPages);
        pageNumbers.forEach(page => {
            if (page === '...') {
                html += '<span class="pagination-ellipsis">...</span>';
            } else {
                html += `
                    <button class="pagination-btn page-number ${page === currentPage ? 'active' : ''}" 
                            data-page="${page}">
                        ${page}
                    </button>
                `;
            }
        });

        // Next button
        html += `
            <button class="pagination-btn ${currentPage === totalPages ? 'disabled' : ''}" 
                    data-page="${currentPage + 1}" 
                    ${currentPage === totalPages ? 'disabled' : ''}>
                Next ›
            </button>
        `;

        html += `
                </div>
            </div>
        `;

        return html;
    }

    /**
     * Get smart page numbers for pagination (with ellipsis for large page counts)
     */
    getPageNumbers(currentPage, totalPages) {
        const pages = [];
        const maxVisible = 7; // Maximum visible page numbers

        if (totalPages <= maxVisible) {
            // Show all pages if total is small
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Always show first page
            pages.push(1);

            if (currentPage > 4) {
                pages.push('...');
            }

            // Show pages around current page
            const start = Math.max(2, currentPage - 1);
            const end = Math.min(totalPages - 1, currentPage + 1);

            for (let i = start; i <= end; i++) {
                if (!pages.includes(i)) {
                    pages.push(i);
                }
            }

            if (currentPage < totalPages - 3) {
                pages.push('...');
            }

            // Always show last page
            if (!pages.includes(totalPages)) {
                pages.push(totalPages);
            }
        }

        return pages;
    }

    /**
     * Go to specific page
     */
    goToPage(page) {
        const newPage = parseInt(page);
        if (newPage >= 1 && newPage <= this.totalPages && newPage !== this.currentPage) {
            this.currentPage = newPage;
            this.onPageChange(this.currentPage);
            return true;
        }
        return false;
    }

    /**
     * Reset to first page
     */
    resetToFirstPage() {
        if (this.currentPage !== 1) {
            this.currentPage = 1;
            this.onPageChange(this.currentPage);
            return true;
        }
        return false;
    }

    /**
     * Update items per page
     */
    setItemsPerPage(itemsPerPage) {
        if (itemsPerPage !== this.itemsPerPage) {
            this.itemsPerPage = itemsPerPage;
            this.currentPage = 1; // Reset to first page
            this.onPageChange(this.currentPage);
            return true;
        }
        return false;
    }

    /**
     * Get current pagination state
     */
    getState() {
        return {
            currentPage: this.currentPage,
            itemsPerPage: this.itemsPerPage,
            totalItems: this.totalItems,
            totalPages: this.totalPages
        };
    }
} 