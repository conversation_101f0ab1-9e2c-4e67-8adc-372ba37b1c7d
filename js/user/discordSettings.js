// Discord Settings Management (Pro Feature)
import { checkProStatus } from './proStatus.js';
import { validateWebhookUrl } from '../integrations/discord.js';
import { AUTO_SEND_CONFIG } from '../../config.js';

/**
 * Save Discord settings (Pro users only)
 * @param {string} webhookUrl - Discord webhook URL
 * @returns {Promise<Object>} - Save result
 */
export async function saveDiscordSettings(webhookUrl) {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return {
                success: false,
                error: 'Discord integration is a Pro feature. Please upgrade to use this functionality.'
            };
        }
        
        // Validate inputs
        if (!webhookUrl) {
            return {
                success: false,
                error: 'Webhook URL is required'
            };
        }
        
        const trimmedUrl = webhookUrl.trim();
        
        if (!validateWebhookUrl(trimmedUrl)) {
            return {
                success: false,
                error: 'Invalid webhook URL format. Expected format: https://discord.com/api/webhooks/ID/TOKEN'
            };
        }
        
        // Save settings
        await chrome.storage.sync.set({
            discordSettings: {
                webhookUrl: trimmedUrl,
                savedAt: new Date().toISOString()
            }
        });
        
        return {
            success: true,
            message: 'Discord settings saved successfully!'
        };
        
    } catch (error) {
        console.error('Error saving Discord settings:', error);
        return {
            success: false,
            error: `Failed to save settings: ${error.message}`
        };
    }
}

/**
 * Get Discord settings (Pro users only)
 * @returns {Promise<Object>} - Settings or null if not configured
 */
export async function getDiscordSettings() {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return null;
        }
        
        const result = await chrome.storage.sync.get(['discordSettings']);
        return result.discordSettings || null;
        
    } catch (error) {
        console.error('Error getting Discord settings:', error);
        return null;
    }
}

/**
 * Clear Discord settings
 * @returns {Promise<boolean>} - Success status
 */
export async function clearDiscordSettings() {
    try {
        await chrome.storage.sync.remove(['discordSettings', AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND]);
        return true;
    } catch (error) {
        console.error('Error clearing Discord settings:', error);
        return false;
    }
}

/**
 * Check if Discord is configured for current user
 * @returns {Promise<boolean>} - Whether Discord is configured
 */
export async function isDiscordConfigured() {
    const settings = await getDiscordSettings();
    return !!(settings && settings.webhookUrl);
}

/**
 * Get masked webhook URL for display
 * @returns {Promise<string>} - Masked URL or empty string
 */
export async function getMaskedWebhookUrl() {
    try {
        const settings = await getDiscordSettings();
        if (!settings || !settings.webhookUrl) {
            return '';
        }
        
        const url = settings.webhookUrl;
        const parts = url.split('/');
        
        if (parts.length < 7) {
            return '*'.repeat(url.length);
        }
        
        const webhookId = parts[5];
        const token = parts[6];
        
        if (token.length <= 8) {
            return `https://discord.com/api/webhooks/${webhookId}/${'*'.repeat(token.length)}`;
        }
        
        return `https://discord.com/api/webhooks/${webhookId}/${token.substring(0, 4)}${'*'.repeat(token.length - 8)}${token.substring(token.length - 4)}`;
        
    } catch (error) {
        console.error('Error getting masked webhook URL:', error);
        return '';
    }
}

/**
 * Validate current Discord configuration
 * @returns {Promise<Object>} - Validation result
 */
export async function validateDiscordConfig() {
    try {
        const settings = await getDiscordSettings();
        
        if (!settings) {
            return {
                isValid: false,
                error: 'No Discord settings found'
            };
        }
        
        if (!settings.webhookUrl) {
            return {
                isValid: false,
                error: 'Incomplete Discord configuration'
            };
        }
        
        if (!validateWebhookUrl(settings.webhookUrl)) {
            return {
                isValid: false,
                error: 'Invalid webhook URL format'
            };
        }
        
        return {
            isValid: true,
            settings: settings
        };
        
    } catch (error) {
        console.error('Error validating Discord config:', error);
        return {
            isValid: false,
            error: `Validation failed: ${error.message}`
        };
    }
}

/**
 * Save Discord auto-send settings (Pro users only)
 * @param {boolean} enabled - Whether auto-send is enabled
 * @returns {Promise<Object>} - Save result
 */
export async function saveDiscordAutoSendSettings(enabled) {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return {
                success: false,
                error: 'Auto-send is a Pro feature. Please upgrade to use this functionality.'
            };
        }
        
        // Check if Discord is configured
        const isConfigured = await isDiscordConfigured();
        if (!isConfigured && enabled) {
            return {
                success: false,
                error: 'Please configure Discord settings before enabling auto-send.'
            };
        }
        
        const currentSettings = await getDiscordAutoSendSettings();
        const newSettings = {
            ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS,
            ...currentSettings,
            enabled: !!enabled,
            updatedAt: new Date().toISOString()
        };
        
        // Reset failure count when enabling
        if (enabled && !currentSettings.enabled) {
            newSettings.failureCount = 0;
        }
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND]: newSettings
        });
        
        return {
            success: true,
            message: `Discord auto-send ${enabled ? 'enabled' : 'disabled'} successfully!`
        };
        
    } catch (error) {
        console.error('Error saving Discord auto-send settings:', error);
        return {
            success: false,
            error: `Failed to save auto-send settings: ${error.message}`
        };
    }
}

/**
 * Get Discord auto-send settings (Pro users only)
 * @returns {Promise<Object>} - Auto-send settings or default settings
 */
export async function getDiscordAutoSendSettings() {
    try {
        // Check Pro status first
        const proStatus = await checkProStatus();
        if (!proStatus.isPro) {
            return { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
        }
        
        const result = await chrome.storage.sync.get([AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND]);
        return result[AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND] || { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
        
    } catch (error) {
        console.error('Error getting Discord auto-send settings:', error);
        return { ...AUTO_SEND_CONFIG.DEFAULT_SETTINGS };
    }
}

/**
 * Update Discord auto-send failure count
 * @param {number} failureCount - New failure count
 * @returns {Promise<boolean>} - Success status
 */
export async function updateDiscordAutoSendFailureCount(failureCount) {
    try {
        const currentSettings = await getDiscordAutoSendSettings();
        const newSettings = {
            ...currentSettings,
            failureCount: failureCount,
            lastFailure: failureCount > 0 ? new Date().toISOString() : null,
            updatedAt: new Date().toISOString()
        };
        
        // Auto-disable if too many failures
        if (failureCount >= AUTO_SEND_CONFIG.MAX_FAILURE_COUNT) {
            newSettings.enabled = false;
            newSettings.autoDisabledAt = new Date().toISOString();
        }
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND]: newSettings
        });
        
        return true;
        
    } catch (error) {
        console.error('Error updating Discord auto-send failure count:', error);
        return false;
    }
}

/**
 * Update Discord auto-send success status
 * @returns {Promise<boolean>} - Success status
 */
export async function updateDiscordAutoSendSuccess() {
    try {
        const currentSettings = await getDiscordAutoSendSettings();
        const newSettings = {
            ...currentSettings,
            failureCount: 0,
            lastSent: new Date().toISOString(),
            lastFailure: null,
            updatedAt: new Date().toISOString()
        };
        
        await chrome.storage.sync.set({
            [AUTO_SEND_CONFIG.STORAGE_KEYS.DISCORD_AUTO_SEND]: newSettings
        });
        
        return true;
        
    } catch (error) {
        console.error('Error updating Discord auto-send success:', error);
        return false;
    }
} 