// Firecrawl Web Scraping Integration Module
import { FIRECRAWL_CONFIG } from '../../config.js';

/**
 * Scrape web content using Firecrawl API
 * @param {string} url - URL to scrape
 * @param {Object} options - Scraping options
 * @returns {Promise<Object>} - Scraped content data
 */
export async function scrapeWebContent(url, options = {}) {
    try {
        // Validate the URL first
        validateScrapeRequest(url);

        const config = {
            url: url,
            ...FIRECRAWL_CONFIG.DEFAULT_OPTIONS,
            ...options
        };

        console.log('🔍 Firecrawl: Starting scrape for URL:', url);

        // Get API key from storage
        const apiKey = await getFirecrawlApiKey();
        if (!apiKey) {
            throw new Error('Firecrawl API key not configured. Please add your API key in Settings.');
        }

        const response = await fetch(FIRECRAWL_CONFIG.API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config),
            signal: AbortSignal.timeout(FIRECRAWL_CONFIG.TIMEOUT)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Scraping failed: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(`Scrape unsuccessful: ${result.error || 'Unknown error'}`);
        }

        console.log('✅ Firecrawl: Scrape completed successfully');
        return processScrapeResponse(result.data);

    } catch (error) {
        console.error('❌ Firecrawl scraping error:', error);
        throw error;
    }
}

/**
 * Rate-limited Firecrawl scraper class
 */
export class FirecrawlScraper {
    constructor() {
        this.lastRequest = 0;
        this.dailyUsage = 0;
        this.lastReset = new Date().toDateString();
    }

    async scrape(url, options = {}) {
        // Check daily usage limit
        this.checkDailyLimit();

        // Enforce rate limiting
        await this.enforceRateLimit();

        // Track usage
        this.trackUsage();

        return await scrapeWebContent(url, options);
    }

    async scrapeWithRetry(url, options = {}, maxRetries = FIRECRAWL_CONFIG.RETRY_ATTEMPTS) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await this.scrape(url, options);
            } catch (error) {
                console.warn(`⚠️ Firecrawl attempt ${attempt} failed:`, error.message);

                if (attempt === maxRetries) {
                    throw error;
                }

                // Exponential backoff
                const backoffMs = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, backoffMs));
            }
        }
    }

    checkDailyLimit() {
        const today = new Date().toDateString();

        // Reset daily counter if it's a new day
        if (today !== this.lastReset) {
            this.dailyUsage = 0;
            this.lastReset = today;
        }

        if (this.dailyUsage >= FIRECRAWL_CONFIG.MAX_DAILY_SCRAPES) {
            throw new Error(`Daily scraping limit reached (${FIRECRAWL_CONFIG.MAX_DAILY_SCRAPES}). Please try again tomorrow.`);
        }
    }

    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequest;

        if (timeSinceLastRequest < FIRECRAWL_CONFIG.RATE_LIMIT_MS) {
            const waitTime = FIRECRAWL_CONFIG.RATE_LIMIT_MS - timeSinceLastRequest;
            console.log(`⏳ Firecrawl: Rate limiting - waiting ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        this.lastRequest = Date.now();
    }

    trackUsage() {
        this.dailyUsage++;
        console.log(`📊 Firecrawl: Daily usage: ${this.dailyUsage}/${FIRECRAWL_CONFIG.MAX_DAILY_SCRAPES}`);
    }
}

/**
 * Process Firecrawl response into standardized format
 * @param {Object} data - Raw Firecrawl response data
 * @returns {Object} - Processed scrape data
 */
function processScrapeResponse(data) {
    const result = {
        content: data.markdown || data.html || '',
        title: data.metadata?.title || '',
        description: data.metadata?.description || '',
        sourceURL: data.metadata?.sourceURL || '',
        statusCode: data.metadata?.statusCode || 200,
        links: data.links || [],
        screenshots: data.actions?.screenshots || [],
        error: data.metadata?.error || null,
        scrapedAt: new Date().toISOString()
    };

    // Validate content
    if (!result.content) {
        console.warn('⚠️ Firecrawl: No content extracted from page');
        result.content = 'No content could be extracted from this page.';
    }

    // Limit content length for analysis
    if (result.content.length > FIRECRAWL_CONFIG.MAX_CONTENT_LENGTH) {
        console.log(`📏 Firecrawl: Content truncated from ${result.content.length} to ${FIRECRAWL_CONFIG.MAX_CONTENT_LENGTH} characters`);
        result.content = result.content.substring(0, FIRECRAWL_CONFIG.MAX_CONTENT_LENGTH) + '\n\n[Content truncated due to length limits]';
    }

    return result;
}

/**
 * Validate scrape request
 * @param {string} url - URL to validate
 */
function validateScrapeRequest(url) {
    if (!url || typeof url !== 'string') {
        throw new Error('URL is required and must be a string');
    }

    // Validate URL format
    try {
        const parsedUrl = new URL(url);

        // Block dangerous protocols
        if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
            throw new Error('Only HTTP and HTTPS URLs are allowed');
        }

        // Block local/private IPs for security
        const hostname = parsedUrl.hostname;
        if (hostname === 'localhost' || 
            hostname.startsWith('192.168.') || 
            hostname.startsWith('10.') ||
            hostname.startsWith('172.16.') ||
            hostname === '127.0.0.1') {
            throw new Error('Local and private IP addresses are not allowed');
        }

    } catch (error) {
        if (error instanceof TypeError) {
            throw new Error('Invalid URL format');
        }
        throw error;
    }
}

/**
 * Get Firecrawl API key from storage
 * @returns {Promise<string|null>} - API key or null if not set
 */
async function getFirecrawlApiKey() {
    try {
        const result = await chrome.storage.local.get(['firecrawlApiKey']);
        return result.firecrawlApiKey || null;
    } catch (error) {
        console.error('Error getting Firecrawl API key:', error);
        return null;
    }
}

/**
 * Save Firecrawl API key to storage
 * @param {string} apiKey - API key to save
 * @returns {Promise<boolean>} - Success status
 */
export async function saveFirecrawlApiKey(apiKey) {
    try {
        if (!apiKey || typeof apiKey !== 'string') {
            throw new Error('Valid API key is required');
        }

        // Basic API key format validation (Firecrawl keys start with 'fc-')
        if (!apiKey.startsWith('fc-')) {
            throw new Error('Invalid Firecrawl API key format. Keys should start with "fc-"');
        }

        await chrome.storage.local.set({ firecrawlApiKey: apiKey });
        console.log('✅ Firecrawl API key saved successfully');
        return true;
    } catch (error) {
        console.error('Error saving Firecrawl API key:', error);
        throw error;
    }
}

/**
 * Remove Firecrawl API key from storage
 * @returns {Promise<boolean>} - Success status
 */
export async function removeFirecrawlApiKey() {
    try {
        await chrome.storage.local.remove(['firecrawlApiKey']);
        console.log('🗑️ Firecrawl API key removed');
        return true;
    } catch (error) {
        console.error('Error removing Firecrawl API key:', error);
        return false;
    }
}

/**
 * Test Firecrawl API connection
 * @returns {Promise<Object>} - Test result
 */
export async function testFirecrawlConnection() {
    try {
        const testUrl = 'https://example.com';
        const scraper = new FirecrawlScraper();
        
        console.log('🧪 Testing Firecrawl connection...');
        const result = await scraper.scrape(testUrl, {
            formats: ['markdown'],
            onlyMainContent: true,
            timeout: 10000 // Shorter timeout for testing
        });

        return {
            success: true,
            message: 'Firecrawl connection test successful!',
            contentLength: result.content.length,
            title: result.title
        };

    } catch (error) {
        console.error('Firecrawl connection test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get masked API key for display
 * @returns {Promise<string>} - Masked API key
 */
export async function getMaskedFirecrawlApiKey() {
    try {
        const apiKey = await getFirecrawlApiKey();
        if (!apiKey) {
            return 'Not configured';
        }
        
        // Show first 6 and last 4 characters
        if (apiKey.length > 10) {
            return `${apiKey.substring(0, 6)}...${apiKey.substring(apiKey.length - 4)}`;
        }
        return '***...***';
    } catch (error) {
        console.error('Error getting masked API key:', error);
        return 'Error';
    }
}

/**
 * Check if Firecrawl is configured
 * @returns {Promise<boolean>} - Configuration status
 */
export async function isFirecrawlConfigured() {
    try {
        const apiKey = await getFirecrawlApiKey();
        return !!apiKey;
    } catch (error) {
        console.error('Error checking Firecrawl configuration:', error);
        return false;
    }
} 