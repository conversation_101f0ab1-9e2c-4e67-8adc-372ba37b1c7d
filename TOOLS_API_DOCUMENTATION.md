# Tools API Documentation

## Base URL
```
https://your-render-app.onrender.com
```

## Authentication
All endpoints require authentication via API key in the header:
```
X-API-Key: your-api-key-here
```

## Endpoints

### 1. List Available Tools
**GET** `/api/tools/list`

Returns a list of all available tools with their descriptions and parameters.

#### Request
```bash
curl -X GET "https://your-render-app.onrender.com/api/tools/list" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

#### JavaScript Example
```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/list', {
  method: 'GET',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data.tools);
```

#### Response
```json
{
  "tools": [
    {
      "name": "crypto-chat",
      "description": "Chat with the AgentHustle AI about crypto and web3 topics",
      "parameters": {
        "type": "object",
        "required": ["message"],
        "properties": {
          "message": {
            "type": "string",
            "description": "The message to send to the crypto assistant"
          }
        }
      }
    },
    {
      "name": "brave_web_search",
      "description": "Performs a web search using the Brave Search API",
      "parameters": {
        "type": "object",
        "required": ["query"],
        "properties": {
          "query": {
            "type": "string",
            "description": "The search query"
          },
          "count": {
            "type": "number",
            "description": "Number of results to return (max 20)",
            "default": 5
          }
        }
      },
      "source": "smithery"
    }
  ]
}
```

### 2. Execute Tool
**POST** `/api/tools/call`

Executes a specific tool with provided parameters.

#### Request Body
```json
{
  "name": "tool-name",
  "params": {
    "param1": "value1",
    "param2": "value2"
  }
}
```

## Available Tools & Examples

### Crypto Chat
Chat with AI about cryptocurrency and web3 topics.

```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "crypto-chat",
    "params": {
      "message": "What is Bitcoin and how does it work?"
    }
  }'
```

```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/call', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'crypto-chat',
    params: {
      message: 'Explain DeFi protocols'
    }
  })
});

const result = await response.json();
console.log(result.result.response);
```

### Web Search (Brave)
Search the web using Brave Search API.

```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "brave_web_search",
    "params": {
      "query": "Bitcoin price today",
      "count": 5
    }
  }'
```

```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/call', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'brave_web_search',
    params: {
      query: 'Ethereum news',
      count: 10
    }
  })
});

const result = await response.json();
console.log(result.result.results);
```

### Local Business Search
Search for local businesses and places.

```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "brave_local_search",
    "params": {
      "query": "crypto ATM near me"
    }
  }'
```

### Ordiscan Tools
Bitcoin ordinals and inscriptions data.

#### Get BRC-20 Token Info
```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/call', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'ordiscan_brc20_info',
    params: {
      ticker: 'ordi'
    }
  })
});
```

#### Get Address BRC-20 Balances
```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ordiscan_address_brc20",
    "params": {
      "address": "bc1p..."
    }
  }'
```

### Stock Analysis Tools
Get stock market data and analysis.

#### Get Stock Data
```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/call', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'get-stock-data',
    params: {
      symbol: 'AAPL'
    }
  })
});
```

### Google Search (Serper)
Enhanced search with rich results.

```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "google_search",
    "params": {
      "q": "cryptocurrency market cap",
      "num": 10
    }
  }'
```

### Firecrawl Tools
Web scraping and crawling capabilities.

#### Scrape Webpage
```javascript
const response = await fetch('https://your-render-app.onrender.com/api/tools/call', {
  method: 'POST',
  headers: {
    'X-API-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'firecrawl_scrape',
    params: {
      url: 'https://coinmarketcap.com',
      formats: ['markdown', 'html']
    }
  })
});
```

#### Crawl Website
```bash
curl -X POST "https://your-render-app.onrender.com/api/tools/call" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "firecrawl_crawl",
    "params": {
      "url": "https://example.com",
      "maxDepth": 2,
      "limit": 10
    }
  }'
```

## Response Format

### Success Response
```json
{
  "success": true,
  "result": {
    // Tool-specific result data
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message describing what went wrong"
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing API key |
| 404 | Not Found - Tool not found |
| 500 | Internal Server Error |

## Rate Limiting
- Default: 100 requests per 15 minutes per API key
- Rate limit headers included in response:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining`
  - `X-RateLimit-Reset`

## Node.js SDK Example

```javascript
class ToolsAPIClient {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async listTools() {
    const response = await fetch(`${this.baseUrl}/api/tools/list`, {
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async callTool(name, params) {
    const response = await fetch(`${this.baseUrl}/api/tools/call`, {
      method: 'POST',
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name, params })
    });
    return response.json();
  }
}

// Usage
const client = new ToolsAPIClient('https://your-render-app.onrender.com', 'your-api-key');

// List available tools
const tools = await client.listTools();
console.log(tools);

// Execute a tool
const result = await client.callTool('brave_web_search', {
  query: 'Bitcoin news',
  count: 5
});
console.log(result);
```

## Python Example

```python
import requests
import json

class ToolsAPIClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def list_tools(self):
        response = requests.get(f"{self.base_url}/api/tools/list", headers=self.headers)
        return response.json()
    
    def call_tool(self, name, params):
        data = {'name': name, 'params': params}
        response = requests.post(
            f"{self.base_url}/api/tools/call", 
            headers=self.headers,
            json=data
        )
        return response.json()

# Usage
client = ToolsAPIClient('https://your-render-app.onrender.com', 'your-api-key')

# List tools
tools = client.list_tools()
print(tools)

# Search web
result = client.call_tool('brave_web_search', {
    'query': 'Ethereum price',
    'count': 3
})
print(result)
```

## Common Tool Parameters

### Search Tools
- `query` (string, required): Search query
- `count` (number, optional): Number of results (default: 5, max: 20)
- `safesearch` (string, optional): 'strict', 'moderate', 'off' (default: 'moderate')

### Crypto Tools  
- `message` (string, required): Message or question about crypto
- `ticker` (string): Token ticker symbol
- `address` (string): Blockchain address

### Stock Tools
- `symbol` (string, required): Stock symbol (e.g., 'AAPL', 'TSLA')
- `interval` (string, optional): Time interval for data

### Web Scraping Tools
- `url` (string, required): URL to scrape or crawl
- `formats` (array, optional): Output formats ['markdown', 'html', 'text']
- `maxDepth` (number, optional): Maximum crawl depth
- `limit` (number, optional): Maximum pages to crawl

## Best Practices

1. **Always check tool availability** by calling `/api/tools/list` first
2. **Handle errors gracefully** - tools may fail due to API limits or network issues
3. **Respect rate limits** - implement exponential backoff for retries
4. **Cache results** when appropriate to reduce API calls
5. **Validate parameters** before making requests
6. **Use appropriate timeouts** for long-running operations like web crawling

## Support

For issues or questions about the Tools API, check the server logs or contact the API administrator. 