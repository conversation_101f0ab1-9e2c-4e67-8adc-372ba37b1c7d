.action-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

    .action-grid {
        grid-template-columns: 1fr;
    }

.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    grid-auto-rows: minmax(160px, auto); /* Ensure consistent minimum height */
    
    /* Override results-container styles to remove card-in-card effect */
    background: none;
    border: none;
    padding: 4px; /* Small padding to avoid scrollbar overlap */
    max-height: none !important; /* Remove the 300px height constraint */
    height: auto; /* Allow natural height expansion */
}

.history-grid .history-item {
    margin-bottom: 0; /* Remove bottom margin as gap is used */
}
