/* <PERSON><PERSON> Styles */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
    color: #F9F9F9;
    border: 1px solid rgba(91, 169, 249, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(91, 169, 249, 0.3);
}

.btn-secondary {
    background: #2C2738;
    color: #B2AFC5;
    border: 1px solid #2C2738;
}

.btn-secondary:hover {
    background: #3A3548;
    color: #F9F9F9;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #B2AFC5;
    border: 2px solid #2C2738;
}

.btn-outline:hover {
    background: #2C2738;
    color: #F9F9F9;
    border-color: #3A3548;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
}

.btn-full {
    width: 100%;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

/* Action Button Styles */
.action-btn {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 18px 20px;
    background: #1D1A2A;
    border: 2px solid #2C2738;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(91, 169, 249, 0.05), transparent);
    transition: left 0.6s;
}

.action-btn:hover {
    transform: translateY(-3px);
    border-color: #5BA9F9;
    box-shadow: 0 8px 25px rgba(91, 169, 249, 0.2);
}

.action-btn:hover::before {
    left: 100%;
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.action-icon.analyze-selection {
    background: linear-gradient(135deg, #FFD84D, #F4BD61);
    color: #2A2C3D;
}

.action-icon.analyze-page {
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    color: #F9F9F9;
}

.action-icon.custom-analysis {
    background: linear-gradient(135deg, #FF5C5C, #FF8A80);
    color: #F9F9F9;
}

.action-icon.crypto-analysis {
    background: linear-gradient(135deg, #F4BD61, #FFD84D);
    color: #2A2C3D;
}

.action-text {
    flex: 1;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 4px;
}

.action-desc {
    font-size: 13px;
    color: #B2AFC5;
    line-height: 1.4;
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: #F9F9F9;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(145deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

/* Enhanced Button Icon Styles */
.btn-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-1px) scale(1.05) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.2s ease !important;
}

.btn-icon:hover::before {
    left: 100%;
}

.btn-icon:focus {
    outline: 2px solid #5BA9F9;
    outline-offset: 2px;
}

.btn-icon.primary:hover {
    background: linear-gradient(135deg, #4A9EF8, #3A2A95);
}

.btn-icon.copy:hover {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.btn-icon.pin:hover {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.btn-icon.edit:hover {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.btn-icon.delete:hover {
    background: linear-gradient(135deg, #dc3545, #c82333);
}
