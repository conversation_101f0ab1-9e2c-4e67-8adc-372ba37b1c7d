.prompt-controls {
    margin-bottom: 20px;
}

.control-row {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.12);
}

.sort-select {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
    min-width: 120px;
}

.sort-select:focus {
    outline: none;
    border-color: #A8C0FF;
}

.sort-select option {
    background: #2A2C3D;
    color: #F9F9F9;
}

.sort-select option:checked,
.sort-select option:hover {
    background: #5BA9F9;
    color: #F9F9F9;
}

.tag-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.tag-filter {
    padding: 4px 10px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    color: #B2AFC5;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-filter:hover {
    background: rgba(255, 255, 255, 0.12);
    color: #F9F9F9;
}

.tag-filter.active {
    background: #5BA9F9;
    border-color: #5BA9F9;
    color: white;
}

.prompt-list {
    max-height: none; /* Remove height constraint to use full available space */
    overflow-y: visible;
    margin-bottom: 16px;
    padding-right: 8px; /* Add space from scrollbar */
    height: auto; /* Allow natural height expansion */
}

.prompt-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    margin-right: 4px; /* Additional margin from scrollbar */
    transition: all 0.3s ease;
}

.prompt-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-1px);
}

.prompt-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.prompt-title {
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    flex: 1;
    min-width: 0;
}

.prompt-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.prompt-content {
    font-size: 14px;
    color: #B2AFC5;
    line-height: 1.4;
    margin-bottom: 16px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    cursor: help;
    transition: all 0.2s ease;
    position: relative;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.prompt-content:hover {
    background: rgba(0, 0, 0, 0.3);
    color: #D1D5DB;
}

.prompt-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.prompt-tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.tag {
    background: rgba(168, 192, 255, 0.15);
    color: #A8C0FF;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tag:hover {
    background: rgba(168, 192, 255, 0.25);
    color: #C5D4FF;
    transform: scale(1.05);
}

.tag:focus {
    outline: 1px solid #A8C0FF;
    outline-offset: 1px;
}

.tag:active {
    transform: scale(0.95);
}

.usage-count {
    background: rgba(255, 255, 255, 0.1);
    color: #B2AFC5;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
}

.filter-tags {
    margin-bottom: 12px;
}

.no-prompts {
    text-align: center;
    color: #B2AFC5;
    font-style: italic;
    padding: 40px 20px;
}

.prompt-date {
    color: #6B7280;
}

.prompt-item.built-in-prompt {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.prompt-item.built-in-prompt:hover {
    border-left-color: #34ce57;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
}

.built-in-badge {
    display: inline-block;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    font-size: 9px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

#restoreBuiltInPrompts {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#restoreBuiltInPrompts:hover {
    background: linear-gradient(135deg, #34ce57, #17a2b8);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.prompt-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt-selector {
    display: flex;
    gap: 8px;
    align-items: center;
}

.prompt-selector select {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
}

.prompt-selector select:focus {
    outline: none;
    border-color: #A8C0FF;
}

.prompt-selector select option {
    background: #2A2C3D;
    color: #F9F9F9;
}

.prompt-selector select option:checked,
.prompt-selector select option:hover {
    background: #5BA9F9;
    color: #F9F9F9;
}

.prompt-selector select optgroup {
    background: #1E1F2A;
    color: #B2AFC5;
    font-weight: 600;
}

.prompt-item.template-prompt {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.08) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.prompt-item.template-prompt:hover {
    border-left-color: #138496;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.15);
}

.template-badge {
    display: inline-block;
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    font-size: 9px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}
