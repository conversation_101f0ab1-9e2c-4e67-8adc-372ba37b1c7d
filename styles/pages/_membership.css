.membership-section {
    border: 1px solid rgba(168, 192, 255, 0.2);
    border-radius: 12px;
    background: rgba(168, 192, 255, 0.05);
}

.membership-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #5BA9F9;
}

.membership-status.regular {
    border-left-color: #B2AFC5;
}

.membership-status.expired {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.membership-status.invalid {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.membership-status.active {
    border-left-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.membership-status.error {
    border-left-color: #FF6B6B;
    background: rgba(255, 107, 107, 0.1);
}

.membership-details {
    margin: 16px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item label {
    font-size: 14px;
    color: #B2AFC5;
    font-weight: 500;
}

.detail-item span {
    font-size: 14px;
    color: #F9F9F9;
    font-weight: 600;
}

.status-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.status-icon {
    font-size: 24px;
}

.status-info h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #F9F9F9;
}

.status-description {
    color: #B2AFC5;
    font-size: 14px;
    margin-top: 4px;
}

.tier-badge {
    background: linear-gradient(145deg, #3F2B96, #A8C0FF);
    color: #F9F9F9;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.expiry-date {
    color: #A8C0FF !important;
}

.days-remaining {
    color: #4CAF50 !important;
}

.days-remaining.warning {
    color: #FFD84D !important;
}

.membership-notes {
    color: #B2AFC5 !important;
    font-style: italic;
}

.expiration-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 216, 77, 0.1);
    border: 1px solid rgba(255, 216, 77, 0.3);
    border-radius: 6px;
    margin: 16px 0;
}

.expiration-warning .warning-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.expiration-warning .warning-text {
    font-size: 14px;
    color: #FFD84D;
    font-weight: 500;
}

.legacy-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(178, 175, 197, 0.1);
    border: 1px solid rgba(178, 175, 197, 0.3);
    border-radius: 6px;
    margin: 16px 0;
}

.legacy-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.legacy-text {
    font-size: 14px;
    color: #B2AFC5;
    font-style: italic;
}

.membership-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.membership-actions .btn {
    flex: 1;
    min-width: 120px;
}

    .membership-details {
        margin: 12px 0;
        padding: 8px;
    }

    .membership-actions {
        flex-direction: column;
    }

    .membership-actions .btn {
        width: 100%;
        min-width: auto;
    }

.key-management-content {
    padding: 0;
    max-width: 100%;
    overflow: hidden;
}

.key-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    overflow: hidden;
    min-width: 0;
}

.key-info label {
    font-size: 14px;
    color: #B2AFC5;
    font-weight: 500;
}

.masked-key {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #F9F9F9;
    background: rgba(0, 0, 0, 0.3);
    padding: 4px 8px;
    border-radius: 4px;
    letter-spacing: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    flex-shrink: 1;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-badge.active {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-badge.invalid {
    background: rgba(255, 107, 107, 0.2);
    color: #FF6B6B;
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.key-action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.key-action-buttons .btn {
    flex: 1;
    min-width: 120px;
}

    .key-action-buttons {
        flex-direction: column;
    }

    .key-action-buttons .btn {
        width: 100%;
        min-width: auto;
    }

    .key-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .masked-key {
        max-width: 100%;
        word-break: break-all;
        white-space: normal;
    }

/* Additional Key Management Styles */
.current-key-section,
.key-actions-section {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.current-key-section h4,
.key-actions-section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #F9F9F9;
    font-weight: 600;
}

.current-key-display {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.key-status-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.key-expires {
    font-size: 13px;
    color: #B2AFC5;
    margin-top: 4px;
}

.key-change-form {
    margin-top: 16px;
    padding: 16px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.key-change-form h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #F9F9F9;
    font-weight: 600;
}

.key-change-form .form-input {
    margin-bottom: 12px;
}

.key-change-form .key-status {
    margin-bottom: 12px;
}

.key-change-form .form-actions {
    margin-top: 12px;
}

.key-change-form .form-actions .btn {
    margin-right: 8px;
}

.error-icon {
    color: #FF6B6B;
    font-size: 18px;
}

.pro-upgrade-notice {
    background: rgba(168, 192, 255, 0.1);
    border: 1px solid rgba(168, 192, 255, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.pro-upgrade-notice h4 {
    margin: 0 0 8px 0;
    color: #A8C0FF;
    font-size: 16px;
    font-weight: 600;
}

.pro-upgrade-notice p {
    margin: 0;
    color: #B2AFC5;
    font-size: 14px;
}
