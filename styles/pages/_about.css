/* Section Styles */
.section {
    padding: 20px;
    animation: fadeIn 0.5s ease-out;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #F9F9F9;
    display: flex;
    align-items: center;
    gap: 8px;
}

.about-content, .help-content {
    padding: 0 10px; /* Add some padding for better spacing */
}

.about-content h2,
.help-content h4 {
    color: #A8C0FF; /* Use a distinct color for titles */
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.about-content h2:first-child,
.help-content h4:first-child {
    margin-top: 0;
}

.about-content p,
.help-content p {
    color: #B2AFC5; /* Lighter text for readability */
    margin-bottom: 1em;
}

.about-content .logo {
    text-align: center;
    margin-bottom: 1em;
}

.about-content .logo svg {
    width: 60px;
    height: 60px;
}

.about-content h2 {
    text-align: center;
    font-size: 20px;
    margin-bottom: 0.25em;
}

.about-content .version {
    text-align: center;
    font-size: 14px;
    color: #B2AFC5;
    margin-bottom: 1em;
}

.about-content .powered-by {
    text-align: center;
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 0.25em;
}

.about-content p {
    text-align: center;
}

/* Section Styles */
.section {
    padding: 20px;
    animation: fadeIn 0.5s ease-out;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #F9F9F9;
    display: flex;
    align-items: center;
    gap: 8px;
}

.about-content, .help-content {
    padding: 0 10px; /* Add some padding for better spacing */
}

.about-content h2,
.help-content h4 {
    color: #A8C0FF; /* Use a distinct color for titles */
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.about-content h2:first-child,
.help-content h4:first-child {
    margin-top: 0;
}

.about-content p,
.help-content p {
    color: #B2AFC5; /* Lighter text for readability */
    margin-bottom: 1em;
}

.about-content .logo {
    text-align: center;
    margin-bottom: 1em;
}

.about-content .logo svg {
    width: 60px;
    height: 60px;
}

.about-content h2 {
    text-align: center;
    font-size: 20px;
    margin-bottom: 0.25em;
}

.about-content .version {
    text-align: center;
    font-size: 14px;
    color: #B2AFC5;
    margin-bottom: 1em;
}

.about-content .powered-by {
    text-align: center;
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 0.25em;
}

.about-content p {
    text-align: center;
}
