.settings-content {
    padding: 0;
}

.settings-group {
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden; /* Prevent content overflow */
}

.settings-group-header h4 {
    color: #A8C0FF;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-description {
    color: #B2AFC5;
    font-size: 14px;
    margin-bottom: 16px;
}

.telegram-configured {
    padding: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.config-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.config-status .status-indicator.connected {
    background: #28a745;
    animation: none;
}

.config-status .status-text {
    color: #28a745;
    font-weight: 500;
}

.config-details {
    margin-bottom: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 40px; /* Ensure consistent height */
    gap: 12px; /* Add gap between label and value */
}

.config-item:last-child {
    border-bottom: none;
}

.config-item label {
    color: #B2AFC5;
    font-size: 14px;
    font-weight: 500;
    flex-shrink: 0; /* Prevent label from shrinking */
    min-width: 80px; /* Ensure minimum width for labels */
}

.config-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.telegram-setup {
    padding: 16px;
    overflow: hidden; /* Prevent content overflow */
}

.setup-instructions {
    margin-bottom: 20px;
    padding: 16px;
    background: rgba(168, 192, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid #A8C0FF;
}

.setup-instructions h5 {
    color: #A8C0FF;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
}

.setup-instructions ol {
    color: #B2AFC5;
    font-size: 14px;
    line-height: 1.6;
    padding-left: 20px;
}

.setup-instructions li {
    margin-bottom: 8px;
}

.setup-instructions a {
    color: #A8C0FF;
    text-decoration: none;
}

.setup-instructions a:hover {
    text-decoration: underline;
}

.setup-instructions code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.setup-form .form-group {
    margin-bottom: 16px;
}

.setup-form .form-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #F9F9F9;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box; /* Include padding in width calculation */
}

.setup-form .form-input:focus {
    outline: none;
    border-color: #A8C0FF;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(168, 192, 255, 0.1);
}

.setup-form .form-input::placeholder {
    color: #666;
}

.telegram-send-btn {
    background: rgba(0, 136, 204, 0.1);
    border: 1px solid rgba(0, 136, 204, 0.3);
    color: #0088cc;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.telegram-send-btn:hover {
    background: rgba(0, 136, 204, 0.2);
    border-color: rgba(0, 136, 204, 0.5);
    transform: translateY(-1px);
}

.telegram-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.telegram-send-btn svg {
    width: 14px;
    height: 14px;
    stroke: currentColor;
}

.discord-send-btn {
    background: rgba(88, 101, 242, 0.1);
    border: 1px solid rgba(88, 101, 242, 0.3);
    color: #5865F2;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.discord-send-btn:hover {
    background: rgba(88, 101, 242, 0.2);
    border-color: rgba(88, 101, 242, 0.5);
    transform: translateY(-1px);
}

.discord-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.discord-send-btn svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.discord-configured {
    padding: 16px;
    overflow: hidden;
}

.discord-setup {
    padding: 16px;
    overflow: hidden;
}

.auto-send-section {
    margin: 16px 0;
    padding: 16px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.auto-send-header {
    margin-bottom: 16px;
}

.auto-send-header h5 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.auto-send-description {
    color: #ccc;
    font-size: 14px;
    margin: 0;
    line-height: 1.4;
}

.auto-send-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background: #fff;
    transition: all 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background: linear-gradient(145deg, #28a745, #20c997);
    border-color: #28a745;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    background: rgba(255, 255, 255, 0.3);
}

.toggle-switch input:checked:hover + .toggle-slider {
    background: linear-gradient(145deg, #20c997, #17a2b8);
}

.toggle-label {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
}

.auto-send-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.auto-send-status.enabled {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.auto-send-status.disabled {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.auto-send-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    margin-bottom: 8px;
}

.warning-text {
    color: #ffc107;
    font-size: 13px;
    font-weight: 500;
}

.auto-send-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: 8px;
    margin-bottom: 8px;
}

.info-text {
    color: #17a2b8;
    font-size: 12px;
    font-weight: 500;
}

    .config-actions {
        flex-direction: column;
    }

    .config-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .telegram-send-btn,
    .discord-send-btn {
        min-width: 28px;
        height: 28px;
        padding: 4px 6px;
    }

    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .config-item label {
        min-width: auto;
    }

    .auto-send-toggle {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .auto-send-section {
        padding: 12px;
    }

    .auto-send-header h5 {
        font-size: 15px;
    }

    .auto-send-description {
        font-size: 13px;
    }

.expiration-warning .warning-text {
    font-size: 14px;
    color: #FFD84D;
    font-weight: 500;
}
