#!/usr/bin/env python3
"""
JavaScript Dependency Visualizer
Creates visual dependency graphs and detects circular dependencies
"""

import os
import re
import json
import sys
import subprocess
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict, deque

class DependencyVisualizer:
    def __init__(self):
        self.dependencies = {}
        self.reverse_dependencies = defaultdict(set)
        self.external_dependencies = set()
        
    def extract_dependencies(self, file_path: str) -> Dict[str, Any]:
        """Extract import/export dependencies from JavaScript file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f"Could not read file: {e}"}
        
        imports = []
        exports = []
        
        # ES6 import patterns
        import_patterns = [
            r'import\s+(?:{[^}]+}|\*\s+as\s+\w+|\w+)\s+from\s+[\'"]([^\'"]+)[\'"]',  # ES6 imports
            r'import\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # Dynamic imports
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',  # CommonJS requires
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            imports.extend(matches)
        
        # Export patterns
        export_patterns = [
            r'export\s+(?:default\s+)?(?:class|function|const|let|var)\s+(\w+)',  # Named exports
            r'export\s+{\s*([^}]+)\s*}',  # Export lists
            r'export\s+default\s+(\w+)',  # Default exports
        ]
        
        for pattern in export_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if '{' in match:  # Export list
                    items = [item.strip().split(' as ')[0] for item in match.split(',')]
                    exports.extend(items)
                else:
                    exports.append(match)
        
        # Separate internal and external dependencies
        internal_imports = []
        external_imports = []
        
        for imp in imports:
            if imp.startswith('./') or imp.startswith('../') or imp.startswith('/'):
                internal_imports.append(imp)
            else:
                external_imports.append(imp)
                self.external_dependencies.add(imp)
        
        return {
            'file': file_path,
            'internal_imports': internal_imports,
            'external_imports': external_imports,
            'exports': exports,
            'total_imports': len(imports),
            'total_exports': len(exports)
        }
    
    def resolve_import_path(self, base_file: str, import_path: str) -> str:
        """Resolve relative import path to absolute path"""
        if import_path.startswith('./') or import_path.startswith('../'):
            base_dir = os.path.dirname(base_file)
            resolved = os.path.normpath(os.path.join(base_dir, import_path))
            
            # Try different extensions
            for ext in ['.js', '.ts', '.jsx', '.tsx']:
                if os.path.exists(resolved + ext):
                    return resolved + ext
            
            # Try index files
            for ext in ['.js', '.ts']:
                index_path = os.path.join(resolved, f'index{ext}')
                if os.path.exists(index_path):
                    return index_path
        
        return import_path
    
    def analyze_directory(self, directory: str) -> Dict[str, Any]:
        """Analyze all JavaScript files in a directory"""
        js_files = []
        for pattern in ['**/*.js', '**/*.ts', '**/*.jsx', '**/*.tsx']:
            js_files.extend(Path(directory).glob(pattern))
        
        file_dependencies = {}
        
        print(f"🔍 Analyzing {len(js_files)} JavaScript files...")
        
        for file_path in js_files:
            file_str = str(file_path)
            deps = self.extract_dependencies(file_str)
            if 'error' not in deps:
                file_dependencies[file_str] = deps
                
                # Build reverse dependency map
                for imp in deps['internal_imports']:
                    resolved_path = self.resolve_import_path(file_str, imp)
                    self.reverse_dependencies[resolved_path].add(file_str)
        
        self.dependencies = file_dependencies
        return file_dependencies
    
    def detect_circular_dependencies(self) -> List[List[str]]:
        """Detect circular dependencies using DFS"""
        visited = set()
        rec_stack = set()
        cycles = []
        
        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            
            # Check dependencies
            if node in self.dependencies:
                for imp in self.dependencies[node]['internal_imports']:
                    resolved = self.resolve_import_path(node, imp)
                    if dfs(resolved, path + [node]):
                        return True
            
            rec_stack.remove(node)
            return False
        
        # Check all files
        for file_path in self.dependencies.keys():
            if file_path not in visited:
                dfs(file_path, [])
        
        return cycles
    
    def generate_dot_graph(self, output_file: str = 'dependency-graph.dot') -> str:
        """Generate DOT format graph for visualization"""
        dot_content = ["digraph Dependencies {"]
        dot_content.append("  rankdir=LR;")
        dot_content.append("  node [shape=box, style=rounded];")
        dot_content.append("")
        
        # Add nodes with colors based on complexity
        for file_path, deps in self.dependencies.items():
            file_name = Path(file_path).stem
            import_count = deps['total_imports']
            export_count = deps['total_exports']
            
            # Color based on complexity
            if import_count > 10 or export_count > 10:
                color = "red"
            elif import_count > 5 or export_count > 5:
                color = "orange"
            else:
                color = "lightblue"
            
            dot_content.append(f'  "{file_name}" [fillcolor={color}, style=filled];')
        
        dot_content.append("")
        
        # Add edges
        for file_path, deps in self.dependencies.items():
            file_name = Path(file_path).stem
            for imp in deps['internal_imports']:
                resolved = self.resolve_import_path(file_path, imp)
                imp_name = Path(resolved).stem
                dot_content.append(f'  "{file_name}" -> "{imp_name}";')
        
        dot_content.append("}")
        
        dot_graph = "\n".join(dot_content)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(dot_graph)
        
        return dot_graph
    
    def generate_svg(self, dot_file: str = 'dependency-graph.dot', svg_file: str = 'dependency-graph.svg') -> bool:
        """Generate SVG visualization from DOT file"""
        try:
            subprocess.run(['dot', '-Tsvg', dot_file, '-o', svg_file], check=True)
            print(f"✅ Dependency graph generated: {svg_file}")
            return True
        except FileNotFoundError:
            print("⚠️  Graphviz not installed. Install with:")
            print("   Windows: choco install graphviz")
            print("   macOS: brew install graphviz")
            print("   Linux: sudo apt install graphviz")
            return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Error generating SVG: {e}")
            return False
    
    def generate_report(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """Generate comprehensive dependency analysis report"""
        report = []
        report.append("🔗 Dependency Analysis Report")
        report.append("=" * 50)
        report.append("")
        
        # Summary statistics
        total_files = len(analysis)
        total_internal_deps = sum(len(deps['internal_imports']) for deps in analysis.values())
        total_external_deps = len(self.external_dependencies)
        
        report.append("📊 Summary:")
        report.append(f"  • Total files analyzed: {total_files}")
        report.append(f"  • Internal dependencies: {total_internal_deps}")
        report.append(f"  • External dependencies: {total_external_deps}")
        report.append("")
        
        # Circular dependencies
        cycles = self.detect_circular_dependencies()
        if cycles:
            report.append("🔄 Circular Dependencies Found:")
            report.append("-" * 30)
            for i, cycle in enumerate(cycles, 1):
                cycle_names = [Path(f).stem for f in cycle]
                report.append(f"{i}. {' → '.join(cycle_names)}")
            report.append("")
            report.append("⚠️  CRITICAL: Resolve circular dependencies before refactoring!")
            report.append("")
        else:
            report.append("✅ No circular dependencies found")
            report.append("")
        
        # Most connected files
        file_connections = []
        for file_path, deps in analysis.items():
            file_name = Path(file_path).stem
            in_degree = len(self.reverse_dependencies.get(file_path, set()))
            out_degree = len(deps['internal_imports'])
            total_connections = in_degree + out_degree
            
            file_connections.append({
                'name': file_name,
                'path': file_path,
                'in_degree': in_degree,
                'out_degree': out_degree,
                'total': total_connections
            })
        
        # Sort by total connections
        file_connections.sort(key=lambda x: x['total'], reverse=True)
        
        report.append("🌐 Most Connected Files (Refactoring Impact):")
        report.append("-" * 40)
        for i, file_info in enumerate(file_connections[:10], 1):
            report.append(f"{i:2d}. {file_info['name']}")
            report.append(f"     Imports: {file_info['out_degree']} | Used by: {file_info['in_degree']} | Total: {file_info['total']}")
        report.append("")
        
        # External dependencies
        if self.external_dependencies:
            report.append("📦 External Dependencies:")
            report.append("-" * 25)
            for dep in sorted(self.external_dependencies):
                report.append(f"  • {dep}")
            report.append("")
        
        # Refactoring recommendations
        report.append("🎯 Refactoring Recommendations:")
        report.append("-" * 30)
        
        if cycles:
            report.append("1. 🚨 URGENT: Resolve circular dependencies first")
        
        high_impact_files = [f for f in file_connections if f['total'] > 5]
        if high_impact_files:
            report.append("2. 🎯 High-impact files (be extra careful when refactoring):")
            for file_info in high_impact_files[:5]:
                report.append(f"   • {file_info['name']} ({file_info['total']} connections)")
        
        isolated_files = [f for f in file_connections if f['total'] == 0]
        if isolated_files:
            report.append("3. 🔧 Isolated files (safe to refactor first):")
            for file_info in isolated_files[:5]:
                report.append(f"   • {file_info['name']}")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 Report saved to: {output_file}")
        
        return report_text

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 dependency_visualizer.py <directory> [output-prefix]")
        print("Example: python3 dependency_visualizer.py js/ dependencies")
        sys.exit(1)
    
    directory = sys.argv[1]
    output_prefix = sys.argv[2] if len(sys.argv) > 2 else 'dependency'
    
    if not os.path.exists(directory):
        print(f"❌ Error: Directory '{directory}' not found")
        sys.exit(1)
    
    print(f"🔍 Analyzing dependencies in {directory}...")
    
    visualizer = DependencyVisualizer()
    analysis = visualizer.analyze_directory(directory)
    
    if not analysis:
        print("❌ No JavaScript files found")
        sys.exit(1)
    
    # Generate DOT graph
    dot_file = f"{output_prefix}-graph.dot"
    svg_file = f"{output_prefix}-graph.svg"
    
    print("📊 Generating dependency graph...")
    visualizer.generate_dot_graph(dot_file)
    visualizer.generate_svg(dot_file, svg_file)
    
    # Generate report
    report_file = f"{output_prefix}-report.txt"
    report = visualizer.generate_report(analysis, report_file)
    print(report)
    
    # Save JSON data
    json_file = f"{output_prefix}-data.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2)
    print(f"📊 JSON data saved to: {json_file}")
    
    # Check for critical issues
    cycles = visualizer.detect_circular_dependencies()
    if cycles:
        print(f"\n🚨 CRITICAL: {len(cycles)} circular dependencies found!")
        print("   Resolve these before refactoring to avoid breaking changes")
    else:
        print(f"\n✅ No circular dependencies found - safe to proceed with refactoring")

if __name__ == "__main__":
    main() 