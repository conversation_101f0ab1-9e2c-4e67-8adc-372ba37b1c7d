<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Hustle Pro Analyzer</title>
<link rel="stylesheet" href="styles/styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="url(#rocketGradient)"/>
                        <defs>
                            <linearGradient id="rocketGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#FF5C5C"/>
                                <stop offset="50%" style="stop-color:#FFD84D"/>
                                <stop offset="100%" style="stop-color:#5BA9F9"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="logo-text">
                    <h1>Agent Hustle</h1>
                    <span class="pro-badge">PRO</span>
                </div>
            </div>
            <div class="tagline">Professional Analysis at Your Fingertips</div>
        </div>

        <div class="main-content-area">
            <!-- API Key Setup -->
            <div id="apiKeySection" class="section">
                <div class="section-header">
                    <h3>🔑 API Configuration</h3>
                </div>
                <div class="input-group">
                    <input type="password" id="apiKeyInput" placeholder="Enter your Agent Hustle API Key">
                    <button id="saveApiKey" class="btn btn-primary">Save</button>
                </div>
                <div class="api-status" id="apiStatus">
                    <span class="status-indicator"></span>
                    <span class="status-text">API Key not configured</span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div id="actionsSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>⚡ Quick Actions</h3>
                </div>
                <div class="action-grid">
                    <button id="analyzeSelection" class="action-btn">
                        <div class="action-icon analyze-selection">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M16 13H8" stroke="currentColor" stroke-width="2"/>
                                <path d="M16 17H8" stroke="currentColor" stroke-width="2"/>
                                <path d="M10 9H9H8" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Analyze Selection</div>
                            <div class="action-desc">Analyze selected text</div>
                        </div>
                    </button>
                    <button id="analyzePage" class="action-btn">
                        <div class="action-icon analyze-page">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M2 12H22" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 2C14.5 4.5 16 8.5 16 12S14.5 19.5 12 22C9.5 19.5 8 15.5 8 12S9.5 4.5 12 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Analyze Page</div>
                            <div class="action-desc">Full page analysis</div>
                        </div>
                    </button>
                    <button id="customAnalysis" class="action-btn">
                        <div class="action-icon custom-analysis">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                                <circle cx="12" cy="12" r="3" fill="currentColor"/>
                                <path d="M12 1V3" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 21V23" stroke="currentColor" stroke-width="2"/>
                                <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2"/>
                                <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2"/>
                                <path d="M1 12H3" stroke="currentColor" stroke-width="2"/>
                                <path d="M21 12H23" stroke="currentColor" stroke-width="2"/>
                                <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2"/>
                                <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Custom Analysis <span class="pro-badge">PRO</span></div>
                            <div class="action-desc">Custom prompt analysis</div>
                        </div>
                    </button>
                    <button id="managePrompts" class="action-btn">
                        <div class="action-icon manage-prompts">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M9 2V6" stroke="currentColor" stroke-width="2"/>
                                <path d="M15 2V6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Manage Prompts <span class="pro-badge">PRO</span></div>
                            <div class="action-desc">Create & organize prompts</div>
                        </div>
                    </button>
                    <button id="scrapeAndAnalyze" class="action-btn">
                        <div class="action-icon scrape-analyze">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 13L14 9M14 9H10M14 9V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M8 21L12 17L16 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Scrape & Analyze 🔗 <span class="pro-badge">PRO</span></div>
                            <div class="action-desc">Scrape any URL and analyze</div>
                        </div>
                    </button>
                    <button id="viewAnalysisHistory" class="action-btn">
                        <div class="action-icon view-history">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" stroke="currentColor" stroke-width="2" fill="none"/>
                                <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">View History</div>
                            <div class="action-desc">See past analyses</div>
                        </div>
                    </button>
                    <button id="startChat" class="action-btn">
                        <div class="action-icon start-chat">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 11.5C21.0034 12.8199 20.6951 14.1219 20.1 15.3C19.3944 16.7118 18.3098 17.8992 16.9674 18.7293C15.6251 19.5594 14.0782 19.9994 12.5 20C11.1801 20.0035 9.87812 19.6951 8.7 19.1L3 21L4.9 15.3C4.30493 14.1219 3.99656 12.8199 4 11.5C4.00061 9.92179 4.44061 8.37488 5.27072 7.03258C6.10083 5.69028 7.28825 4.60574 8.7 3.90003C9.87812 3.30496 11.1801 2.99659 12.5 3.00003H13C15.0843 3.11502 17.053 3.99479 18.5291 5.47089C20.0052 6.94699 20.885 8.91568 21 11V11.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="action-text">
                            <div class="action-title">Start Chat <span class="pro-badge">PRO</span></div>
                            <div class="action-desc">Interactive AI conversations</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Chat Section -->
            <div id="chatSection" class="section dock-mode" style="display: none;">
                <div class="section-header">
                    <h3>💬 Agent Hustle Chat</h3>
                    <button id="backToActionsFromChat" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="chat-container with-dock">
                    <!-- Chat Header with Title -->
                    <div class="chat-header">
                        <div class="chat-title-container">
                            <div class="chat-naming-container">
                                <input type="text" id="chatTitle" class="chat-title" placeholder="Chat Title" value="New Chat" readonly>
                                <button id="chatRenameBtn" class="chat-rename-btn chat-pro-feature" title="Rename Chat (Pro)">
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 21H21M5 15L16 4L20 8L9 19L3 21L5 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="chat-status-indicator" id="chatStatusIndicator"></div>
                        </div>
                    </div>
                    
                    <div class="chat-messages" id="chatMessages">
                        <!-- Chat messages will be dynamically inserted here -->
                    </div>
                    <div class="chat-input-area">
                        <div class="chat-input-container">
                            <textarea id="chatInput" placeholder="Type your message..." rows="2"></textarea>
                            <button id="sendChatMessage" class="btn btn-primary chat-send-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                        <div class="chat-status" id="chatStatus">
                            <span class="chat-status-text">Ready to chat</span>
                        </div>
                    </div>
                </div>
                
                <!-- Legacy Chat Actions (hidden when dock is active) -->
                <div class="chat-actions dock-active">
                    <button id="newChatSession" class="btn btn-outline btn-sm">New Chat</button>
                    <button id="chatHistory" class="btn btn-outline btn-sm">History</button>
                    <button id="clearChat" class="btn btn-outline btn-sm">Clear</button>
                </div>
                
                <!-- Modern Chat Dock -->
                <div class="chat-dock" id="chatDock">
                    <div class="dock-icon new-chat" id="dockNewChat" data-action="new">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <div class="dock-tooltip">New Chat</div>
                    </div>
                    
                    <div class="dock-separator"></div>
                    
                    <div class="dock-icon history" id="dockHistory" data-action="history">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12A9 9 0 1 0 12 3A9 9 0 0 0 3 12ZM12 7V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <div class="dock-tooltip">History</div>
                    </div>
                    
                    <div class="dock-icon clear" id="dockClear" data-action="clear">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 6H21M8 6V4C8 3.44772 8.44772 3 9 3H15C15.5523 3 16 3.44772 16 4V6M19 6V20C19 20.5523 18.5523 21 18 21H6C5.44772 21 5 20.5523 5 20V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <div class="dock-tooltip">Clear Chat</div>
                    </div>
                    
                    <div class="dock-separator"></div>
                    
                    <div class="dock-icon settings" id="dockSettings" data-action="settings">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.0113 9.78251C4.2806 9.5899 4.4831 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15V15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <div class="dock-tooltip">Settings</div>
                    </div>
                </div>
            </div>

            <!-- URL Scrape Form -->
            <div id="scrapeForm" class="section" style="display: none;">
                <div class="section-header">
                    <h3>🔥 Firecrawl Scraper</h3>
                    <button id="backToActionsFromScrape" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="main-content-area">
                    <div class="scrape-container">
                        <div class="form-group url-input-group">
                            <label for="scrapeUrlDirect" class="url-label">URL to Scrape:</label>
                            <input type="url" id="scrapeUrlDirect" placeholder="https://dexscreener.com/solana/7p1f3rzuv9493veoa7nb" class="firecrawl-url-input">
                        </div>
                        <div class="form-actions scrape-actions">
                            <button id="analyzeScrapeUrl" class="btn-scrape-analyze">
                                <span class="scrape-icon">🔥</span>
                                Scrape & Analyze
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Analysis Form -->
            <div id="customForm" class="section" style="display: none;">
                <div class="section-header">
                    <h3>🎯 Custom Analysis</h3>
                    <button id="backToActions" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="form-group">
                    <label for="savedPromptsSelect">Saved Prompts:</label>
                    <div class="prompt-selector">
                        <select id="savedPromptsSelect">
                            <option value="">Choose a saved prompt or create new...</option>
                        </select>
                        <button id="loadSelectedPrompt" class="btn btn-outline btn-sm">Load</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="customPrompt">Analysis Prompt:</label>
                    <textarea id="customPrompt" placeholder="Enter your custom analysis prompt..."></textarea>
                </div>
                <div class="form-group">
                    <label>Data Source:</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="dataSource" value="selection" checked>
                            <span class="radio-custom"></span>
                            Selected Text
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="dataSource" value="page">
                            <span class="radio-custom"></span>
                            Full Page
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="dataSource" value="url">
                            <span class="radio-custom"></span>
                            URL Scraping 🔗 <span class="pro-badge">PRO</span>
                        </label>
                    </div>
                </div>
                <div class="form-group" id="urlInputGroup" style="display: none;">
                    <label for="scrapeUrl">URL to Scrape:</label>
                    <input type="url" id="scrapeUrl" placeholder="https://example.com/article" class="url-input">
                    <small>Enter any web URL to scrape and analyze its content using Firecrawl</small>
                </div>
                <div class="form-actions">
                    <button id="runCustomAnalysis" class="btn btn-primary">
                        <span class="btn-icon">🚀</span>
                        Run Analysis
                    </button>
                    <button id="saveCurrentPrompt" class="btn btn-outline">
                        <span class="btn-icon">💾</span>
                        Save Prompt
                    </button>
                </div>
            </div>

            <!-- Prompt Management Section -->
            <div id="promptManagementSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>📝 Manage Prompts</h3>
                    <button id="backToActionsFromPrompts" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                
                <!-- Prompt Management Controls -->
                <div class="prompt-controls">
                    <div class="control-row">
                        <input type="text" id="promptSearch" placeholder="Search prompts..." class="search-input">
                        <select id="promptSort" class="sort-select">
                            <option value="recent">Recent</option>
                            <option value="alphabetical">A-Z</option>
                            <option value="usage">Most Used</option>
                        </select>
                        <button id="addNewPrompt" class="btn btn-primary btn-sm">+ New</button>
                    </div>
                    <div class="filter-tags" id="filterTags">
                        <!-- Tag filters will be populated here -->
                    </div>
                </div>

                <!-- Prompt List -->
                <div id="promptList" class="prompt-list">
                    <!-- Prompts will be populated here -->
                </div>

                <!-- Pagination Controls -->
                <div id="promptPagination" class="pagination-wrapper">
                    <!-- Pagination controls will be populated here -->
                </div>

                <!-- Import/Export -->
                <div class="prompt-actions">
                    <button id="exportPrompts" class="btn btn-outline btn-sm">📤 Export</button>
                    <input type="file" id="importPromptsFile" accept=".json" style="display: none;">
                    <button id="importPrompts" class="btn btn-outline btn-sm">📥 Import</button>
                </div>


            </div>

            <!-- Prompt Editor Modal -->
            <div id="promptEditorModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="promptEditorTitle">New Prompt</h3>
                        <button id="closePromptEditor" class="btn btn-secondary btn-sm">✕</button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="promptTitle">Title:</label>
                            <input type="text" id="promptTitle" placeholder="Enter prompt title...">
                        </div>
                        <div class="form-group">
                            <label for="promptContent">Content:</label>
                            <textarea id="promptContent" placeholder="Enter your prompt content..." rows="6"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="promptTags">Tags (comma-separated):</label>
                            <input type="text" id="promptTags" placeholder="e.g. SEO, Marketing, Analysis">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="promptPinned">
                                <span class="checkbox-custom"></span>
                                Pin this prompt
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancelPromptEdit" class="btn btn-secondary">Cancel</button>
                        <button id="savePromptEdit" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>

            <!-- Analysis Results -->
            <div id="resultsSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>📊 Analysis Results</h3>
                    <button id="backToActionsFromResults" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div id="analysisResults" class="results-container">
                    <!-- Results will be populated here -->
                </div>
                <div class="results-actions">
                    <button id="copyResults" class="btn btn-outline">📋 Copy</button>
                    <button id="exportResults" class="btn btn-outline">💾 Export</button>
                </div>
            </div>

            <!-- Pro Upgrade Section -->
            <div id="upgradeSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>🚀 Upgrade to Pro</h3>
                    <button id="backToActionsFromUpgrade" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="upgrade-content">
                    <div class="upgrade-hero">
                        <div class="upgrade-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="url(#starGradient)" stroke="currentColor" stroke-width="1"/>
                                <defs>
                                    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FFD84D"/>
                                        <stop offset="100%" style="stop-color:#FF5C5C"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <h2>🚀 Unlock Pro Power</h2>
                        <p>Transform your workflow with advanced AI analysis tools and professional prompt management</p>
                        <div class="upgrade-stats">
                            <span class="stat-item">⚡ 10x Faster Analysis</span>
                            <span class="stat-item">📝 Unlimited Custom Prompts</span>
                            <span class="stat-item">🎯 Professional Templates</span>
                        </div>
                    </div>
                    
                    <div class="upgrade-features">
                        <div class="feature-item featured">
                            <span class="feature-icon">🎯</span>
                            <div class="feature-text">
                                <strong>Custom Analysis Engine</strong>
                                <p>Create unlimited custom prompts for SEO, security reviews, content analysis, and specialized insights. Pre-loaded with professional templates.</p>
                                <div class="feature-highlight">Most Popular Feature</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📝</span>
                            <div class="feature-text">
                                <strong>Professional Prompt Library</strong>
                                <p>Advanced prompt management with tagging, search, pagination, import/export, and usage tracking. Organize hundreds of prompts effortlessly.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🚀</span>
                            <div class="feature-text">
                                <strong>Productivity Accelerators</strong>
                                <p>One-click prompt loading, bulk operations, pinned favorites, and smart categorization. Save hours of repetitive work.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🔧</span>
                            <div class="feature-text">
                                <strong>Flexible Analysis Sources</strong>
                                <p>Analyze selected text, full pages, or custom content with your personalized prompts. Perfect for research, content creation, and analysis workflows.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">📊</span>
                            <div class="feature-text">
                                <strong>Built-in Professional Templates</strong>
                                <p>Ready-to-use prompts for SEO analysis, security reviews, content summaries, and more. Start being productive immediately.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💾</span>
                            <div class="feature-text">
                                <strong>Data Portability & Backup</strong>
                                <p>Export/import your entire prompt library. Never lose your custom workflows. Share templates with your team.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pro-key-form">
                        <div class="form-group">
                            <label for="proKeyInput">Enter Your Pro Key:</label>
                            <input type="password" id="proKeyInput" placeholder="Enter your pro key..." class="pro-key-input">
                            <div class="key-status" id="proKeyStatus">
                                <span class="status-indicator"></span>
                                <span class="status-text">Enter your pro key to validate</span>
                            </div>
                        </div>
                        <button id="validateProKey" class="btn btn-primary btn-full">
                            <span class="btn-icon">🔑</span>
                            Validate Pro Key
                        </button>
                        <button id="clearProCache" class="btn btn-outline btn-full" style="margin-top: 10px;">
                            <span class="btn-icon">🗑️</span>
                            Clear Cache & Reset
                        </button>
                    </div>
                    
                    <div class="upgrade-footer">
                        <div class="social-proof">
                            <p class="testimonial">"HustlePlug Pro transformed my content workflow. The custom prompts save me hours every day!" - Professional User</p>
                        </div>
                        <div class="cta-section">
                            <p class="cta-text">Ready to supercharge your productivity?</p>
                            <p>Don't have a Pro key? <a href="https://agenthustle.ai/" target="_blank" style="color: #5BA9F9; font-weight: bold;">Get Pro Access →</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis History -->
            <div id="analysisHistorySection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>📜 History</h3>
                    <button id="backToActionsFromHistory" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                
                <!-- History Tabs -->
                <div class="history-tabs">
                    <button id="analysisHistoryTab" class="history-tab active">📊 Analysis</button>
                    <button id="chatHistoryTab" class="history-tab">💬 Chat</button>
                </div>
                
                <!-- Analysis History -->
                <div id="analysisHistoryContent" class="history-content">
                    <div id="analysisHistoryList" class="history-grid">
                        <!-- Analysis history will be populated here -->
                    </div>
                    <div id="historyPagination" class="pagination-wrapper">
                        <!-- Pagination controls will be populated here -->
                    </div>
                </div>
                
                <!-- Chat History -->
                <div id="chatHistoryContent" class="history-content" style="display: none;">
                    <div class="chat-history-actions">
                        <button id="newChatFromHistory" class="btn btn-primary btn-sm">💬 New Chat</button>
                        <button id="exportChatHistory" class="btn btn-outline btn-sm">📤 Export</button>
                        <button id="clearAllChatHistory" class="btn btn-outline btn-sm">🗑️ Clear All</button>
                    </div>
                    <div id="chatHistoryList" class="history-grid">
                        <!-- Chat history will be populated here -->
                    </div>
                    <div id="chatHistoryPagination" class="pagination-wrapper">
                        <!-- Chat pagination controls will be populated here -->
                    </div>
                </div>
            </div>

            <!-- About Section -->
            <div id="aboutSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>ℹ️ About Agent Hustle</h3>
                    <button id="backToActionsFromAbout" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="about-content">
                    <div class="logo">
                        <div class="logo-icon">
                            <svg width="60" height="60" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="url(#rocketGradient)"></path>
                            </svg>
                        </div>
                    </div>
                    <h2>Agent Hustle Pro Analyzer</h2>
                    <p class="version">Version 1.0.3</p>
                    <p>Professional AI-powered analysis at your fingertips.</p>
                    <p class="powered-by">Powered by Agent Hustle AI</p>
                    <p>Advanced AI tools for crypto, web, and more.</p>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settingsSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>⚙️ Settings</h3>
                    <button id="backToActionsFromSettings" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="settings-content">
                    <!-- Membership Information -->
                    <div class="settings-group membership-section">
                        <div class="settings-group-header">
                            <h4>👤 Membership Information</h4>
                            <p class="settings-description">Your current membership status and details</p>
                        </div>
                        <div id="membershipInfoContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Telegram Integration (Pro Feature) -->
                    <div class="settings-group pro-feature-section">
                        <div class="settings-group-header">
                            <h4>🔗 Telegram Integration <span class="pro-badge">PRO</span></h4>
                            <p class="settings-description">Send analysis results directly to your Telegram chat</p>
                        </div>
                        <div id="telegramSettingsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Discord Integration (Pro Feature) -->
                    <div class="settings-group pro-feature-section">
                        <div class="settings-group-header">
                            <h4>💬 Discord Integration <span class="pro-badge">PRO</span></h4>
                            <p class="settings-description">Send analysis results directly to your Discord channel</p>
                        </div>
                        <div id="discordSettingsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Chat Auto-Send (Pro Feature) -->
                    <div class="settings-group pro-feature-section">
                        <div class="settings-group-header">
                            <h4>💬 Auto-Send to Chat <span class="pro-badge">PRO</span></h4>
                            <p class="settings-description">Automatically send analysis results to your chat sessions</p>
                        </div>
                        <div id="chatAutoSendSettingsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Firecrawl Web Scraping (Pro Feature) -->
                    <div class="settings-group pro-feature-section">
                        <div class="settings-group-header">
                            <h4>🔗 Firecrawl Web Scraping <span class="pro-badge">PRO</span></h4>
                            <p class="settings-description">Scrape and analyze content from any web URL</p>
                        </div>
                        <div id="firecrawlSettings">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div id="helpSection" class="section" style="display: none;">
                <div class="section-header">
                    <h3>❓ Help & Usage</h3>
                    <button id="backToActionsFromHelp" class="btn btn-secondary btn-sm">← Back</button>
                </div>
                <div class="help-content">
                    <h4>How to Use Agent Hustle Pro</h4>
                    <p><strong>Full Page Analysis:</strong> Click the "Analyze Page" button to analyze the entire content of the current browser tab.</p>
                    <p><strong>Text Selection Analysis:</strong> If Full Page Analysis doesn't work as expected or if you want to analyze a specific portion of the page, simply select the text you want to analyze and then click the "Analyze Selection" button.</p>
                    
                    <h4>Common Issues</h4>
                    <p><strong>"Full Page Analysis" not working:</strong> Some websites have complex structures that can interfere with the full-page content extraction. If you encounter this, please use the "Analyze Selection" method as a reliable alternative.</p>
                    <p><strong>API Key Issues:</strong> Ensure your API key is entered correctly and is active. You can manage your API key from the settings section.</p>

                    <h4>More Help</h4>
                    <p><strong>Getting Your API Key:</strong> To use Agent Hustle Pro, you need to create a vault and get your API key from <a href="https://agenthustle.ai/" target="_blank" style="color: #5BA9F9; text-decoration: none;">https://agenthustle.ai/</a>. Once you have your API key, enter it in the configuration section above.</p>
                    <p><strong>Need Support?</strong> For additional help and documentation, visit the official Agent Hustle platform or contact our support team.</p>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loadingSection" class="section" style="display: none;">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">
                        <h3>🧠 AI Analysis in Progress</h3>
                        <p>Agent Hustle is analyzing your data...</p>
                        <div class="loading-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Footer -->
        <div class="footer">
            <div class="footer-links">
                <a href="#" id="settingsLink">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M19.4 15C19.2 15.3 19.2 15.7 19.4 16L20.4 17.9C20.6 18.3 20.5 18.8 20.1 19.1L18.9 20.1C18.6 20.4 18.1 20.4 17.8 20.1L15.8 19.1C15.5 18.9 15.1 18.9 14.8 19.1C14.5 19.3 14.3 19.5 14 19.7C13.7 19.9 13.5 20.2 13.5 20.6V22.5C13.5 23 13.1 23.4 12.6 23.4H11.4C10.9 23.4 10.5 23 10.5 22.5V20.6C10.5 20.2 10.3 19.9 10 19.7C9.7 19.5 9.5 19.3 9.2 19.1C8.9 18.9 8.5 18.9 8.2 19.1L6.2 20.1C5.9 20.4 5.4 20.4 5.1 20.1L3.9 19.1C3.5 18.8 3.4 18.3 3.6 17.9L4.6 16C4.8 15.7 4.8 15.3 4.6 15C4.4 14.7 4.2 14.5 4 14.2C3.8 13.9 3.5 13.7 3.1 13.7H1.2C0.7 13.7 0.3 13.3 0.3 12.8V11.6C0.3 11.1 0.7 10.7 1.2 10.7H3.1C3.5 10.7 3.8 10.5 4 10.2C4.2 9.9 4.4 9.7 4.6 9.4C4.8 9.1 4.8 8.7 4.6 8.4L3.6 6.4C3.4 6 3.5 5.5 3.9 5.2L5.1 4.2C5.4 3.9 5.9 3.9 6.2 4.2L8.2 5.2C8.5 5.4 8.9 5.4 9.2 5.2C9.5 5 9.7 4.8 10 4.6C10.3 4.4 10.5 4.1 10.5 3.7V1.8C10.5 1.3 10.9 0.9 11.4 0.9H12.6C13.1 0.9 13.5 1.3 13.5 1.8V3.7C13.5 4.1 13.7 4.4 14 4.6C14.3 4.8 14.5 5 14.8 5.2C15.1 5.4 15.5 5.4 15.8 5.2L17.8 4.2C18.1 3.9 18.6 3.9 18.9 4.2L20.1 5.2C20.5 5.5 20.6 6 20.4 6.4L19.4 8.4C19.2 8.7 19.2 9.1 19.4 9.4C19.6 9.7 19.8 9.9 20 10.2C20.2 10.5 20.5 10.7 20.9 10.7H22.8C23.3 10.7 23.7 11.1 23.7 11.6V12.8C23.7 13.3 23.3 13.7 22.8 13.7H20.9C20.5 13.7 20.2 13.9 20 14.2C19.8 14.5 19.6 14.7 19.4 15Z" stroke="currentColor" stroke-width="2" fill="none"/>
                    </svg>
                    Settings
                </a>
                <a href="#" id="helpLink">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M9.09 9C9.32 8.4 9.77 7.9 10.37 7.68C10.97 7.46 11.64 7.5 12.2 7.8C12.76 8.1 13.18 8.63 13.37 9.27C13.56 9.91 13.5 10.6 13.18 11.19C12.86 11.78 12.31 12.22 11.66 12.42C11.01 12.62 10.31 12.56 9.7 12.26" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 17H12.01" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Help
                </a>
                <a href="#" id="aboutLink">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 16V12" stroke="currentColor" stroke-width="2"/>
                        <path d="M12 8H12.01" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    About
                </a>
            </div>
        </div>
    </div>

    <!-- Key Management Modal -->
    <div id="keyManagementModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔑 Manage Pro Key</h3>
                <button id="closeKeyManagement" class="btn btn-secondary btn-sm">×</button>
            </div>
            <div class="modal-body">
                <div id="keyManagementContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Naming Modal -->
    <div id="chatNamingModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>💬 New Chat</h3>
                <button id="closeChatNaming" class="btn btn-secondary btn-sm">×</button>
            </div>
            <div class="modal-body">
                <div id="chatNamingContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="popup.js" type="module"></script>
</body>
</html> 