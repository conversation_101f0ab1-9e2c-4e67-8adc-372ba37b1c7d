// Cache clearing and pro key reset utility
// Run this script to clear all cached data and reset pro key validation

console.log('🧹 Pro Key Cache Clearing and Reset Utility');
console.log('===========================================\n');

// Simulate Chrome storage for testing
const mockStorage = {
    local: {
        data: {},
        get: function(keys) {
            return Promise.resolve(keys === null ? this.data : 
                typeof keys === 'string' ? { [keys]: this.data[keys] } :
                keys.reduce((result, key) => {
                    result[key] = this.data[key];
                    return result;
                }, {}));
        },
        set: function(items) {
            Object.assign(this.data, items);
            return Promise.resolve();
        },
        remove: function(keys) {
            const keysArray = Array.isArray(keys) ? keys : [keys];
            keysArray.forEach(key => delete this.data[key]);
            return Promise.resolve();
        },
        clear: function() {
            this.data = {};
            return Promise.resolve();
        }
    },
    sync: {
        data: {},
        get: function(keys) {
            return Promise.resolve(keys === null ? this.data : 
                typeof keys === 'string' ? { [keys]: this.data[keys] } :
                keys.reduce((result, key) => {
                    result[key] = this.data[key];
                    return result;
                }, {}));
        },
        set: function(items) {
            Object.assign(this.data, items);
            return Promise.resolve();
        },
        remove: function(keys) {
            const keysArray = Array.isArray(keys) ? keys : [keys];
            keysArray.forEach(key => delete this.data[key]);
            return Promise.resolve();
        },
        clear: function() {
            this.data = {};
            return Promise.resolve();
        }
    }
};

// Mock chrome API for testing
global.chrome = {
    storage: mockStorage
};

async function clearAllProCache() {
    console.log('🗑️ Step 1: Clearing all pro cache data...');
    
    try {
        // Clear local storage cache
        const localResult = await chrome.storage.local.get(null);
        const localKeysToRemove = Object.keys(localResult).filter(key => 
            key.startsWith('proStatus_') || 
            key.startsWith('usage_') ||
            key.includes('proCache') ||
            key.includes('proKey')
        );
        
        if (localKeysToRemove.length > 0) {
            await chrome.storage.local.remove(localKeysToRemove);
            console.log(`   ✅ Removed ${localKeysToRemove.length} local cache entries`);
            console.log(`   📋 Keys removed: ${localKeysToRemove.join(', ')}`);
        } else {
            console.log('   ℹ️ No local cache entries found');
        }
        
        // Clear sync storage pro data
        const syncResult = await chrome.storage.sync.get(null);
        const syncKeysToRemove = Object.keys(syncResult).filter(key => 
            key.includes('hustleProStatus') ||
            key.includes('proCache') ||
            key.includes('proValidation')
        );
        
        if (syncKeysToRemove.length > 0) {
            await chrome.storage.sync.remove(syncKeysToRemove);
            console.log(`   ✅ Removed ${syncKeysToRemove.length} sync cache entries`);
            console.log(`   📋 Keys removed: ${syncKeysToRemove.join(', ')}`);
        } else {
            console.log('   ℹ️ No sync cache entries found');
        }
        
    } catch (error) {
        console.error('   ❌ Error clearing cache:', error);
    }
}

async function checkCurrentProKey() {
    console.log('\n🔍 Step 2: Checking current pro key status...');
    
    try {
        const result = await chrome.storage.sync.get(['hustleProKey']);
        const proKey = result.hustleProKey;
        
        if (proKey) {
            console.log(`   ✅ Pro key found: ${proKey.substring(0, 4)}...${proKey.substring(proKey.length - 4)}`);
            console.log(`   📏 Key length: ${proKey.length} characters`);
            console.log(`   🔤 Key format: ${proKey.includes('-') ? 'Hyphenated' : 'Continuous'}`);
            return proKey;
        } else {
            console.log('   ⚠️ No pro key found in storage');
            return null;
        }
    } catch (error) {
        console.error('   ❌ Error checking pro key:', error);
        return null;
    }
}

async function resetProKeyValidation(proKey) {
    console.log('\n🔄 Step 3: Resetting pro key validation...');
    
    if (!proKey) {
        console.log('   ⚠️ No pro key to reset');
        return;
    }
    
    try {
        // Clear the specific key from storage and re-add it
        await chrome.storage.sync.remove(['hustleProKey', 'hustleProStatus']);
        console.log('   ✅ Cleared existing pro key data');
        
        // Re-add the key (this will trigger fresh validation)
        await chrome.storage.sync.set({
            hustleProKey: proKey.trim(),
            hustleProStatus: {
                isPro: false, // Reset to false to force revalidation
                lastChecked: new Date().toISOString(),
                cached: false,
                resetAt: new Date().toISOString()
            }
        });
        console.log('   ✅ Pro key reset and ready for fresh validation');
        
    } catch (error) {
        console.error('   ❌ Error resetting pro key:', error);
    }
}

async function simulateKeyValidation() {
    console.log('\n🧪 Step 4: Simulating key validation process...');
    
    try {
        // This would normally import from the actual validator
        console.log('   ℹ️ In browser extension, this would trigger:');
        console.log('   📞 validateProKey(proKey, true) // Force validation');
        console.log('   🔄 Fresh API call to validation endpoint');
        console.log('   💾 New cache entry with current timestamp');
        console.log('   ✅ Updated UI with fresh validation result');
        
    } catch (error) {
        console.error('   ❌ Error in validation simulation:', error);
    }
}

async function showInstructions() {
    console.log('\n📋 Step 5: Manual Steps to Complete the Fix');
    console.log('==========================================');
    console.log('1. 🔧 Open Chrome extension popup');
    console.log('2. 🔍 Go to Settings section');
    console.log('3. 🗑️ Click "Clear Cache" if available');
    console.log('4. 📝 Re-enter your pro key');
    console.log('5. ✅ Click "Validate" or "Save"');
    console.log('6. 🔄 Wait for fresh validation (may take 2-5 seconds)');
    console.log('7. 🎉 Your valid key should now be accepted!');
    
    console.log('\n🔧 Alternative Fix in Browser Console:');
    console.log('======================================');
    console.log('1. 🌐 Open Chrome DevTools (F12)');
    console.log('2. 📝 Go to Console tab');
    console.log('3. 📋 Paste this code:');
    console.log('');
    console.log('// Clear all pro cache data');
    console.log('chrome.storage.local.clear();');
    console.log('chrome.storage.sync.remove(["hustleProStatus"]);');
    console.log('console.log("Cache cleared! Re-enter your pro key.");');
    console.log('');
    console.log('4. ⏎ Press Enter to run');
    console.log('5. 🔄 Refresh the extension popup');
    console.log('6. 📝 Re-enter your pro key');
}

// Main execution
async function main() {
    try {
        await clearAllProCache();
        const proKey = await checkCurrentProKey();
        await resetProKeyValidation(proKey);
        await simulateKeyValidation();
        await showInstructions();
        
        console.log('\n🎯 Summary');
        console.log('==========');
        console.log('✅ Cache clearing completed');
        console.log('✅ Pro key reset completed');
        console.log('⚠️ Manual validation required in extension');
        console.log('');
        console.log('🔧 The "Invalid pro key" error should be resolved after re-entering your key in the extension.');
        console.log('💡 This issue was likely caused by cached data from an old key or validation format.');
        
    } catch (error) {
        console.error('❌ Error in main execution:', error);
    }
}

// Run the utility
main().catch(console.error); 