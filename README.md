# 🚀 <PERSON> Pro Analyzer - Chrome Extension

Professional AI-powered analysis at your fingertips. Analyze selected text or entire web pages with <PERSON>'s advanced AI capabilities.

## ✨ Features

### 🎯 Analysis Types
- **📝 Text Selection Analysis** - Analyze any selected text on web pages
- **🌐 Full Page Analysis** - Comprehensive analysis of entire webpage content
- **₿ Crypto & Blockchain Analysis** - Specialized analysis for crypto-related content
- **🎯 Custom Analysis** - Use your own prompts for specific analysis needs
- **📋 Quick Summary** - Fast summaries of content
- **✅ Fact Checking** - Verify claims and statements
- **😊 Sentiment Analysis** - Analyze tone and emotional content

### 🛠️ Powerful Features
- **Right-click Context Menu** - Quick access to analysis tools
- **Keyboard Shortcuts** - Fast analysis with hotkeys
- **Selection Tooltips** - Instant analysis suggestions for selected text
- **In-page Results Panel** - Beautiful analysis results displayed on the page
- **Copy & Export** - Save and share your analysis results
- **Secure API Key Storage** - Your credentials are safely stored locally

### 🔧 Advanced Capabilities
- **AI-Powered Tools** - Access to 40+ specialized AI tools
- **Crypto Analysis Tools** - Security checks, rug detection, market analysis
- **Web Search Integration** - Real-time information gathering
- **Stock Analysis** - Financial market insights
- **Web Scraping Tools** - Extract structured data from websites

## 🚀 Installation

### Method 1: Chrome Web Store (Coming Soon)
1. Visit the Chrome Web Store
2. Search for "Agent Hustle Pro Analyzer"
3. Click "Add to Chrome"

### Method 2: Developer Mode (Current)
1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension folder
5. The extension will appear in your toolbar

## ⚙️ Setup

### 1. Get Your API Key
1. Sign up for Agent Hustle API access
2. Get your API key from the dashboard
3. Copy your API key

### 2. Configure the Extension
1. Click the Agent Hustle extension icon in your toolbar
2. Enter your API key in the configuration section
3. Click "Save" - you're ready to go!

## 📖 Usage Guide

### Quick Start
1. **Select text** on any webpage
2. **Right-click** and choose "🚀 Agent Hustle Pro Analyzer"
3. **Choose analysis type** from the context menu
4. **View results** in the beautiful analysis panel

### Analysis Methods

#### 📝 Text Selection Analysis
1. Select any text on a webpage
2. Right-click → "📝 Analyze Selected Text"
3. Get comprehensive insights and analysis

#### 🌐 Page Analysis
1. Right-click anywhere on a page
2. Choose "🌐 Analyze Entire Page"
3. Get full page content analysis

#### ₿ Crypto Analysis
1. On crypto-related pages or selected crypto content
2. Right-click → "₿ Crypto Analysis"
3. Get security assessment, risk analysis, and market insights

#### 🎯 Custom Analysis
1. Click the extension icon
2. Choose "Custom Analysis"
3. Enter your own analysis prompt
4. Select data source (selection or full page)
5. Get tailored analysis results

### Keyboard Shortcuts
- **Ctrl/Cmd + Shift + A** - Analyze selected text
- **Ctrl/Cmd + Shift + P** - Analyze page
- **Ctrl/Cmd + Shift + C** - Crypto analysis
- **Escape** - Close analysis panel

### Selection Tooltips
- Select text (10+ characters)
- See "🚀 Analyze with Agent Hustle" tooltip
- Click to instantly analyze

## 🔧 Features in Detail

### Context Menu Options
- **📝 Analyze Selected Text** - Deep analysis of selected content
- **🌐 Analyze Entire Page** - Comprehensive page analysis
- **₿ Crypto Analysis** - Specialized blockchain analysis
- **📋 Quick Summary** - Fast content summarization
- **✅ Fact Check** - Verify claims and statements
- **😊 Sentiment Analysis** - Emotional tone analysis

### Analysis Results
- **Professional formatting** with markdown support
- **Tool execution details** when AI tools are used
- **Copy to clipboard** functionality
- **Export as JSON** for further processing
- **Session tracking** for analysis history

### Security & Privacy
- **Local API key storage** - Keys stored securely in Chrome sync storage
- **No data collection** - Your analysis data stays private
- **Secure HTTPS connections** - All API calls are encrypted
- **Minimal permissions** - Only necessary permissions requested

## 🛡️ API Integration

This extension connects to Agent Hustle's powerful API platform:

- **Base URL**: `https://enhanced-cli-boilerplate-exp5-b5.onrender.com`
- **Endpoint**: `/api/agentui/chat`
- **Authentication**: API Key via `X-API-Key` header
- **40+ AI Tools** available for enhanced analysis

### Available AI Tools
- **Crypto Tools**: rugcheck, trending-tokens, wallet-balance
- **Web Search**: brave-search, google_search, serper_search
- **Bitcoin/Ordinals**: 29+ specialized Bitcoin tools
- **Stock Analysis**: real-time stock data and alerts
- **Web Scraping**: firecrawl tools for data extraction

## 🎨 UI/UX Features

### Modern Design
- **Gradient backgrounds** with professional styling
- **Smooth animations** and transitions
- **Responsive design** for all screen sizes
- **Dark mode support** for comfortable viewing
- **Accessibility features** with proper focus management

### User Experience
- **Instant feedback** with loading states and progress indicators
- **Error handling** with helpful error messages
- **Success notifications** for completed actions
- **Auto-hide features** to avoid interface clutter
- **Mobile-friendly** responsive design

## 🔧 Development

### Project Structure
```
agent-hustle-extension/
├── manifest.json          # Extension manifest
├── popup.html             # Main popup interface
├── popup.js               # Popup functionality
├── background.js          # Service worker
├── content.js             # Content script
├── content.css            # Content styles
├── styles/
│   └── popup.css          # Popup styles
├── icons/                 # Extension icons
└── README.md              # This file
```

### Technologies Used
- **Manifest V3** - Latest Chrome extension standard
- **Vanilla JavaScript** - No external dependencies
- **Modern CSS** - Flexbox, Grid, CSS Variables
- **Chrome APIs** - Storage, Scripting, Context Menus
- **Fetch API** - Modern HTTP requests

### Building & Testing
1. Load extension in developer mode
2. Test on various websites
3. Check console for errors
4. Verify API integration
5. Test all analysis types

## 📋 Requirements

- **Chrome Browser** version 88 or higher
- **Agent Hustle API Key** for full functionality
- **Internet connection** for AI analysis
- **Active tab permissions** for content analysis

## 🐛 Troubleshooting

### Common Issues

#### API Key Not Working
- Verify your API key is correct
- Check if the API service is running
- Ensure you have proper API permissions

#### Analysis Not Starting
- Check your internet connection
- Verify the extension has necessary permissions
- Try refreshing the page and retrying

#### Context Menu Not Appearing
- Ensure the extension is enabled
- Check if you're on a supported page (not chrome:// pages)
- Try reloading the extension

#### Results Panel Not Showing
- Check if the page allows content injection
- Verify no other extensions are conflicting
- Try on a different website

### Getting Help
1. Check the browser console for error messages
2. Verify your API key configuration
3. Test on multiple websites
4. Contact support with specific error details

## 🔄 Updates & Changelog

### Version 1.0.0
- Initial release
- Full analysis capabilities
- Context menu integration
- Keyboard shortcuts
- Modern UI design
- Secure API key storage

## 📞 Support

For issues, questions, or feature requests:

1. **Check the troubleshooting section** above
2. **Review the usage guide** for proper setup
3. **Contact support** with detailed information about your issue

## 🔗 Related Links

- [Agent Hustle API Documentation](./API_INTEGRATION_GUIDE.md)
- [External API Integration Guide](./EXTERNAL_API_INTEGRATION_GUIDE.md)
- [Chrome Extension Developer Guide](https://developer.chrome.com/docs/extensions/)

## 📄 License

This extension is provided as-is for use with Agent Hustle API services.

---

**🚀 Agent Hustle Pro Analyzer** - Professional AI analysis, right in your browser. 