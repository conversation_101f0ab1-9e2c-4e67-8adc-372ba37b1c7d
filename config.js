export const API_ENDPOINT = 'https://agenthustle.ai/api/chat';

// Pro Key Validation Endpoint - Primary validation (Turso-powered)
export const PRO_VALIDATION_ENDPOINT = 'https://plugin-api-4m2r.onrender.com/api';

// OLD: Vercel endpoint (backup)
// export const PRO_VALIDATION_ENDPOINT = 'https://hustleplug-pro-validation-hva21keg3-calel33s-projects.vercel.app/api';

// Fallback GitHub Pages URLs for offline validation
export const PRO_KEYS_ENDPOINT = 'https://calel33.github.io/json-validate/pro-keys-hashed.json';
export const PRO_KEYS_ENHANCED_ENDPOINT = 'https://calel33.github.io/json-validate/pro-keys-enhanced.json';

// Option 2: Local hardcoded pro keys (uncomment to use instead of remote)
// export const LOCAL_PRO_KEYS = [
//     'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
//     'f6e5d4c3b2a1098765432109876543210987654321fedcba0987654321fedcba'
// ];

export const PRO_SALT = 'AgentHustle2024ProSalt!@#$%^&*()_+SecureKey';

// Simple Membership Configuration
export const MEMBERSHIP_CONFIG = {
    STATUS: {
        ACTIVE: 'active',
        EXPIRED: 'expired',
        SUSPENDED: 'suspended'
    },
    TIERS: {
        PRO: 'pro',
        PREMIUM: 'premium'
    },
    DEFAULT_DURATION: 365 * 24 * 60 * 60 * 1000, // 1 year in milliseconds
    WARNING_DAYS: [7, 3, 1], // Days before expiration to show warnings
    API_ENDPOINT: 'https://calel33.github.io/json-validate/pro-keys-enhanced.json'
};

// Pagination Configuration
export const PAGINATION_CONFIG = {
    HISTORY_ITEMS_PER_PAGE: 10,
    PROMPTS_ITEMS_PER_PAGE: 8
};

// Auto-Send Configuration (Pro Feature)
export const AUTO_SEND_CONFIG = {
    STORAGE_KEYS: {
        DISCORD_AUTO_SEND: 'discordAutoSendSettings',
        TELEGRAM_AUTO_SEND: 'telegramAutoSendSettings'
    },
    DEFAULT_SETTINGS: {
        enabled: false,
        lastSent: null,
        failureCount: 0
    },
    MAX_FAILURE_COUNT: 3,
    RETRY_DELAY: 1000
};

// Telegram Integration Configuration (Pro Feature)
export const TELEGRAM_CONFIG = {
    API_BASE_URL: 'https://api.telegram.org/bot',
    MAX_MESSAGE_LENGTH: 4096,
    PARSE_MODE: 'HTML', // Changed from 'Markdown' to 'HTML' for better compatibility
    RETRY_ATTEMPTS: 3,
    TIMEOUT: 10000
};

// Discord Integration Configuration (Pro Feature)
export const DISCORD_CONFIG = {
    MAX_EMBED_LENGTH: 6000,
    MAX_FIELD_VALUE_LENGTH: 1024,
    RETRY_ATTEMPTS: 3,
    TIMEOUT: 10000
};

// Firecrawl Web Scraping Configuration (Pro Feature)
export const FIRECRAWL_CONFIG = {
    API_ENDPOINT: 'https://api.firecrawl.dev/v1/scrape',
    RATE_LIMIT_MS: 1000, // 1 second between requests
    TIMEOUT: 30000, // 30 seconds
    MAX_CONTENT_LENGTH: 8000, // Max content for analysis
    MAX_DAILY_SCRAPES: 100, // Daily usage limit tracking
    RETRY_ATTEMPTS: 3,
    DEFAULT_OPTIONS: {
        formats: ['markdown'],
        onlyMainContent: true,
        blockAds: true,
        removeBase64Images: true,
        timeout: 30000,
        mobile: false
    }
}; 