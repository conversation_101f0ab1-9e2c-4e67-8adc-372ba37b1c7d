// Agent Hustle Pro Analyzer - Content Script

// Initialize content script
console.log('Agent Hustle Pro Analyzer content script loaded');

// Global variables
let isAnalyzing = false;
let analysisPanel = null;
let selectionHighlight = null;
let currentTooltipText = null; // To prevent re-rendering tooltip for the same text

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

function init() {
    console.log('Initializing Agent Hustle Pro Analyzer content script');
    
    // Add selection highlighting
    setupSelectionHighlighting();
    
    // Add keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Listen for messages from popup/background
    setupMessageListeners();
    
    // Add floating action button (optional)
    // setupFloatingActionButton();
    
    // Add custom styles for Agent Hustle UI elements
    injectStyles();
}

function injectStyles() {
    const styleSheet = 'content.css';
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = chrome.runtime.getURL(styleSheet);
    (document.head || document.documentElement).appendChild(link);
    console.log('Agent Hustle Pro Analyzer styles injected.');
}

// Selection highlighting for better UX
function setupSelectionHighlighting() {
    let highlightTimeout;
    
    document.addEventListener('mouseup', (event) => {
        clearTimeout(highlightTimeout);
        
        highlightTimeout = setTimeout(() => {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            
            if (selectedText && selectedText.length > 10) {
                // If tooltip is already shown for the exact same text, do nothing.
                if (selectionHighlight && currentTooltipText === selectedText) {
                    return;
                }
                showSelectionTooltip(selection);
                currentTooltipText = selectedText; // Store the text for which tooltip is shown
            } else {
                hideSelectionTooltip();
            }
        }, 100);
    });
    
    document.addEventListener('mousedown', (event) => {
        // If the mousedown is on the tooltip itself, do not hide it.
        if (selectionHighlight && selectionHighlight.contains(event.target)) {
            return;
        }
        // Hide tooltip when clicking elsewhere
        hideSelectionTooltip();
    });
    
    // Also hide tooltip when user starts selecting new text
    document.addEventListener('selectstart', () => {
        hideSelectionTooltip();
    });
}

// Show tooltip for selected text
function showSelectionTooltip(selection) {
    hideSelectionTooltip();
    
    if (selection.rangeCount === 0) return;
    
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    // Store the selected text immediately before it gets cleared
    const selectedText = selection.toString().trim();
    
    const tooltipWidth = 200; 
    const tooltipHeight = 36;

    // Simple positioning - always below selection, centered
    let topPos = rect.bottom + 8;
    let leftPos = rect.left + (rect.width / 2) - (tooltipWidth / 2);

    // Keep within viewport bounds with simple clamping
    if (leftPos < 10) leftPos = 10;
    if (leftPos + tooltipWidth > window.innerWidth - 10) {
        leftPos = window.innerWidth - tooltipWidth - 10;
    }
    
    // If too close to bottom, place above
    if (topPos + tooltipHeight > window.innerHeight - 10) {
        topPos = rect.top - tooltipHeight - 8;
    }
    
    // If still doesn't fit, place in middle of viewport
    if (topPos < 10) {
        topPos = 10;
    }

    // Create tooltip with fixed dimensions
    const tooltip = document.createElement('div');
    tooltip.id = 'agent-hustle-selection-tooltip';
    tooltip.style.cssText = `
        position: fixed;
        top: ${topPos}px;
        left: ${leftPos}px;
        width: ${tooltipWidth}px;
        height: ${tooltipHeight}px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 0;
        border-radius: 18px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        z-index: 999999;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        border: 1px solid rgba(255,255,255,0.2);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        user-select: none;
        transition: transform 0.15s ease, box-shadow 0.15s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        pointer-events: auto;
        box-sizing: border-box;
        transform-origin: center center;
        will-change: transform;
        white-space: nowrap;
        overflow: hidden;
    `;
    
    tooltip.textContent = '🚀 Analyze with Agent Hustle';
    
    // Simpler hover effects that don't change size much
    tooltip.addEventListener('mouseenter', () => {
        tooltip.style.transform = 'translateY(-1px)';
        tooltip.style.boxShadow = '0 6px 16px rgba(0,0,0,0.4)';
    });
    
    tooltip.addEventListener('mouseleave', () => {
        tooltip.style.transform = 'translateY(0)';
        tooltip.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
    });
    
    // Handle click to analyze - use stored selectedText
    tooltip.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('Tooltip clicked! Analyzing text:', selectedText.substring(0, 50) + '...');
        analyzeSelectedTextWithContent(selectedText);
        hideSelectionTooltip();
    });
    
    // Prevent tooltip from interfering with text selection
    tooltip.addEventListener('mousedown', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });
    
    document.body.appendChild(tooltip);
    selectionHighlight = tooltip;
    
    console.log('Tooltip created at position:', topPos, leftPos);
    
    // Auto-hide after 8 seconds
    setTimeout(() => {
        hideSelectionTooltip();
    }, 8000);
}

// Hide selection tooltip
function hideSelectionTooltip() {
    if (selectionHighlight) {
        selectionHighlight.remove();
        selectionHighlight = null;
        currentTooltipText = null; // Clear the stored text
    }
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + Shift + A - Analyze selection
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
            e.preventDefault();
            analyzeSelectedText();
        }
        
        // Ctrl/Cmd + Shift + P - Analyze page
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            analyzePage();
        }
        
        // Ctrl/Cmd + Shift + C - Crypto analysis
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
            e.preventDefault();
            analyzeCrypto();
        }
        
        // Escape - Close analysis panel
        if (e.key === 'Escape') {
            closeAnalysisPanel();
        }
    });
}

// Message listeners for communication with popup/background
function setupMessageListeners() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        switch (request.action) {
            case 'analyzeSelection':
                analyzeSelectedText();
                sendResponse({ success: true });
                break;
                
            case 'analyzePage':
                analyzePage();
                sendResponse({ success: true });
                break;
                
            case 'analyzeCrypto':
                analyzeCrypto();
                sendResponse({ success: true });
                break;
                
            case 'getPageData':
                sendResponse(getPageData());
                break;
                
            case 'getSelectedText':
                sendResponse({ text: window.getSelection().toString() });
                break;
                
            case 'closePanel':
                closeAnalysisPanel();
                sendResponse({ success: true });
                break;
        }
    });
}

// New function to analyze with pre-stored text content
async function analyzeSelectedTextWithContent(selectedText) {
    if (!selectedText) {
        showNotification('No text selected for analysis', 'warning');
        return;
    }
    
    if (selectedText.length < 10) {
        showNotification('Please select more text for analysis', 'warning');
        return;
    }
    
    console.log('Opening popup and triggering analysis for:', selectedText.substring(0, 100) + '...');
    
    try {
        // First, store the selected text for the popup to use
        await chrome.storage.local.set({ 
            pendingAnalysis: {
                type: 'selection',
                data: selectedText,
                timestamp: Date.now()
            }
        });
        
        // Open the popup
        await chrome.runtime.sendMessage({ action: 'openPopup' });
        
        // Send message to trigger analysis in popup
        setTimeout(() => {
            chrome.runtime.sendMessage({ 
                action: 'triggerAnalysis',
                type: 'selection',
                data: selectedText
            });
        }, 500); // Small delay to ensure popup is open
        
        showNotification('Opening Agent Hustle analyzer...', 'info');
        
    } catch (error) {
        console.error('Error opening popup for analysis:', error);
        showNotification('Failed to open analyzer', 'error');
    }
}

// Analysis functions
async function analyzeSelectedText() {
    const selectedText = window.getSelection().toString().trim();
    return analyzeSelectedTextWithContent(selectedText);
}

async function analyzePage() {
    const pageData = getPageData();
    
    // Send message to background script to handle analysis
    chrome.runtime.sendMessage({
        action: 'analyzeFromContent',
        type: 'page',
        data: pageData
    });
    
    showNotification('Analyzing page content...', 'info');
}

async function analyzeCrypto() {
    const selectedText = window.getSelection().toString().trim();
    const pageData = getPageData();
    
    // Send message to background script to handle analysis
    chrome.runtime.sendMessage({
        action: 'analyzeFromContent',
        type: 'crypto',
        data: {
            selectedText,
            pageData
        }
    });
    
    showNotification('Performing crypto analysis...', 'info');
}

// Get page data for analysis
function getPageData() {
    const title = document.title;
    const url = window.location.href;
    const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
    
    // Get main content (try to avoid navigation, ads, etc.)
    const contentSelectors = [
        'main',
        'article',
        '.content',
        '.post-content',
        '.entry-content',
        '#content',
        '.main-content',
        '[role="main"]'
    ];
    
    let mainContent = '';
    for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            mainContent = element.innerText;
            break;
        }
    }
    
    // Fallback to body content if no main content found
    if (!mainContent) {
        mainContent = document.body.innerText;
    }
    
    // Limit content length to avoid API limits
    const maxLength = 8000;
    if (mainContent.length > maxLength) {
        mainContent = mainContent.substring(0, maxLength) + '...';
    }
    
    // Look for crypto-related keywords
    const cryptoKeywords = [
        'bitcoin', 'btc', 'ethereum', 'eth', 'solana', 'sol',
        'crypto', 'cryptocurrency', 'blockchain', 'defi',
        'nft', 'token', 'coin', 'trading', 'wallet', 'dex',
        'yield', 'staking', 'mining', 'hodl', 'altcoin'
    ];
    
    const foundKeywords = cryptoKeywords.filter(keyword => 
        mainContent.toLowerCase().includes(keyword)
    );
    
    return {
        title,
        url,
        metaDescription,
        content: mainContent,
        foundKeywords,
        timestamp: new Date().toISOString()
    };
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.agent-hustle-notification');
    existingNotifications.forEach(notification => notification.remove());
    
    const notification = document.createElement('div');
    notification.className = 'agent-hustle-notification';
    
    const colors = {
        info: '#667eea',
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545'
    };
    
    const icons = {
        info: '🚀',
        success: '✅',
        warning: '⚠️',
        error: '❌'
    };
    
    notification.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${colors[type]};
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            z-index: 10001;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            animation: slideInDown 0.3s ease-out;
            max-width: 400px;
            text-align: center;
        ">
            ${icons[type]} ${message}
        </div>
        <style>
            @keyframes slideInDown {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        </style>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutUp 0.3s ease-out';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 4000);
}

// Close analysis panel
function closeAnalysisPanel() {
    const panel = document.getElementById('agent-hustle-analysis-panel');
    if (panel) {
        panel.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            panel.remove();
        }, 300);
    }
}

// Optional: Floating action button
function setupFloatingActionButton() {
    // Only show on pages with substantial content
    if (document.body.innerText.length < 500) return;
    
    const fab = document.createElement('div');
    fab.id = 'agent-hustle-fab';
    fab.innerHTML = `
        <div style="
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            font-size: 24px;
            user-select: none;
        " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
            🚀
        </div>
    `;
    
    fab.addEventListener('click', () => {
        // Open popup or show quick actions menu
        chrome.runtime.sendMessage({ action: 'openPopup' });
    });
    
    document.body.appendChild(fab);
    
    // Hide FAB when scrolling (optional)
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        fab.style.opacity = '0.5';
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            fab.style.opacity = '1';
        }, 1000);
    });
}

// Smart text extraction for better analysis
function extractSmartContent() {
    // Remove script and style elements
    const scripts = document.querySelectorAll('script, style, nav, header, footer, aside');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = document.body.innerHTML;
    
    scripts.forEach(script => {
        const tempScript = tempDiv.querySelector(script.tagName.toLowerCase());
        if (tempScript) tempScript.remove();
    });
    
    return tempDiv.innerText;
}

// Detect if page is crypto-related
function isCryptoPage() {
    const pageText = document.body.innerText.toLowerCase();
    const cryptoKeywords = [
        'bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi',
        'nft', 'token', 'coin', 'trading', 'wallet'
    ];
    
    return cryptoKeywords.some(keyword => pageText.includes(keyword));
}

// Auto-suggest analysis based on page content
function autoSuggestAnalysis() {
    if (isCryptoPage()) {
        // Could show a subtle suggestion for crypto analysis
        console.log('Crypto-related content detected');
    }
}

// Initialize auto-suggestions
setTimeout(autoSuggestAnalysis, 2000);

// Export functions for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getPageData,
        isCryptoPage,
        extractSmartContent
    };
} 