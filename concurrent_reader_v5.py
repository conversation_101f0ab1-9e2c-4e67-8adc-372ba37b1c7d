import asyncio
import aiofiles
import time
import sys
import os
import glob
import json
import subprocess
import tempfile
import functools
from concurrent.futures import ThreadPoolExecutor
from typing import List, Tuple, Dict, Any

def ensure_context_folder():
    """Create context folder if it doesn't exist relative to the script's CWD."""
    context_dir = "context"
    if not os.path.exists(context_dir):
        os.makedirs(context_dir)
        print(f"📁 Created context folder: {context_dir}")
    return context_dir

def check_ctags_installed():
    """Check if the ctags executable is available."""
    try:
        subprocess.run(["ctags", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

async def read_file_content_and_save(filepath: str, context_dir: str) -> Dict[str, Any]:
    """
    Asynchronously read the content of a file and save it to context folder.
    
    Args:
        filepath: Path to the file to read
        context_dir: Directory to save context files
        
    Returns:
        Dict with processing results
    """
    print(f"Starting to read: {filepath}")
    
    # Simulate I/O latency to demonstrate concurrency
    await asyncio.sleep(0.1)
    
    try:
        async with aiofiles.open(filepath, mode='r', encoding='utf-8') as f:
            content = await f.read()
        
        print(f"Finished reading: {filepath}")
        
        # Save content to individual JSON file in context folder
        base_filename = os.path.basename(filepath)
        content_filename = os.path.splitext(base_filename)[0] + ".content.json"
        content_path = os.path.join(context_dir, content_filename)
        
        file_data = {
            "path": filepath,
            "filename": base_filename,
            "content": content,
            "lines": len(content.split('\n')),
            "size_bytes": len(content.encode('utf-8'))
        }
        
        async with aiofiles.open(content_path, mode='w', encoding='utf-8') as out_f:
            await out_f.write(json.dumps(file_data, indent=2))
        
        print(f"📄 Content saved to: {content_path}")
        
        return {
            "path": filepath,
            "status": "success",
            "content": content,
            "output_file": content_path,
            "lines": file_data["lines"],
            "size_bytes": file_data["size_bytes"]
        }
        
    except FileNotFoundError:
        error_msg = f"File not found: {filepath}"
        print(f"❌ {error_msg}")
        return {"path": filepath, "status": "error", "error": error_msg}
    except PermissionError:
        error_msg = f"Permission denied: {filepath}"
        print(f"❌ {error_msg}")
        return {"path": filepath, "status": "error", "error": error_msg}
    except UnicodeDecodeError:
        error_msg = f"Cannot decode file (not text): {filepath}"
        print(f"❌ {error_msg}")
        return {"path": filepath, "status": "error", "error": error_msg}
    except Exception as e:
        error_msg = f"Error reading {filepath}: {e}"
        print(f"❌ {error_msg}")
        return {"path": filepath, "status": "error", "error": str(e)}

def generate_tags_json_for_file_sync(filepath: str, context_dir: str) -> Dict[str, Any]:
    """
    Generate ctags for a file and save as JSON (synchronous version for thread executor).
    
    Args:
        filepath: Path to the file to generate tags for
        context_dir: Directory to save tag files
        
    Returns:
        Dict with tag generation results
    """
    print(f"⚙️ Starting tag generation for: {filepath}")
    
    try:
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tagfile = tmp.name

        # Generate ctags for the single file
        result = subprocess.run([
            "ctags", "-f", tagfile, "--fields=+n", filepath
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            os.remove(tagfile)
            error_msg = f"ctags failed for {filepath}: {result.stderr}"
            print(f"❌ Failed to generate tags for {os.path.basename(filepath)}: ctags error")
            return {"path": filepath, "status": "error", "error": error_msg}

        tags = []
        with open(tagfile, "r", encoding="utf-8") as f:
            for line in f:
                if line.startswith("!_TAG_"):
                    continue  # Skip metadata lines
                parts = line.strip().split("\t")
                if len(parts) < 4:
                    continue
                symbol, file, pattern, meta = parts[:4]
                kind = meta.split(":")[-1] if ":" in meta else meta
                tags.append({
                    "symbol": symbol,
                    "kind": kind,
                    "line": pattern.strip("/^$"),
                    "pattern": pattern
                })

        os.remove(tagfile)

        # Create filename for tags in context folder
        base_filename = os.path.basename(filepath)
        tag_filename = os.path.splitext(base_filename)[0] + ".tags.json"
        tag_path = os.path.join(context_dir, tag_filename)

        # Save tags to JSON file
        with open(tag_path, "w", encoding="utf-8") as out_f:
            json.dump(tags, out_f, indent=2)

        print(f"✅ Finished tag generation for: {filepath}")
        print(f"🏷️ Tags saved to: {tag_path}")
        
        return {
            "path": filepath,
            "status": "success",
            "output_file": tag_path,
            "tags_count": len(tags)
        }

    except Exception as e:
        error_msg = f"Failed to generate tags for {filepath}: {e}"
        print(f"❌ Failed to generate tags for {os.path.basename(filepath)}: {e}")
        return {"path": filepath, "status": "error", "error": str(e)}

def get_workspace_files() -> List[str]:
    """
    Get a list of common source files in the current workspace.
    
    Returns:
        List of file paths
    """
    current_dir = os.getcwd()
    
    # Define patterns for common source files
    patterns = [
        "*.py", "*.js", "*.ts", "*.jsx", "*.tsx", "*.md", "*.txt", 
        "*.json", "*.yaml", "*.yml", "*.ini", "*.cfg", "*.toml"
    ]
    
    files = []
    for pattern in patterns:
        files.extend(glob.glob(os.path.join(current_dir, pattern)))
    
    # Remove any directories and ensure we only have files
    files = [f for f in files if os.path.isfile(f)]
    
    return files

async def main(file_paths: List[str], max_workers: int = 8, generate_tags: bool = True, max_display_lines: int = None):
    """
    Main orchestrator function for concurrent file reading and tag generation.
    
    Args:
        file_paths: List of file paths to read
        max_workers: Maximum number of worker threads for tag generation
        generate_tags: Whether to generate ctags
        max_display_lines: Maximum lines to display per file (None = show all)
    """
    if not file_paths:
        print("📁 No files provided. Discovering files in workspace...")
        workspace_files = get_workspace_files()
        
        if not workspace_files:
            print("❌ No readable files found in the workspace.")
            print("Usage: python concurrent_reader_v5.py <file1> <file2> ...")
            return
        
        print(f"📋 Found {len(workspace_files)} files in workspace:")
        for f in workspace_files:
            print(f"   - {os.path.basename(f)}")
        
        file_paths = workspace_files
    
    # Ensure context folder exists
    context_dir = ensure_context_folder()
    
    # Check ctags availability if tag generation is enabled
    ctags_available = check_ctags_installed() if generate_tags else False
    if generate_tags and not ctags_available:
        print("⚠️ ctags not found. Tag generation will be skipped.")
        generate_tags = False
    
    print(f"\n🚀 Starting concurrent reading of {len(file_paths)} files...")
    if generate_tags:
        print("🏷️ Tag generation enabled")
    if max_display_lines:
        print(f"📺 Display limited to {max_display_lines} lines per file")
    else:
        print("📺 Full content display enabled")
    
    start_time = time.perf_counter()
    
    loop = asyncio.get_running_loop()
    
    # Create tasks for reading and saving content (async)
    read_save_tasks = [read_file_content_and_save(fp, context_dir) for fp in file_paths]
    
    # Create tasks for tag generation (run synchronous function in executor)
    tag_gen_tasks = []
    if generate_tags:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            tag_gen_tasks = [
                loop.run_in_executor(
                    executor,
                    functools.partial(generate_tags_json_for_file_sync, fp, context_dir)
                )
                for fp in file_paths
            ]
    
    # Wait for all read/save tasks to complete
    print("⏳ Waiting for file reading and content saving to complete...")
    read_results = await asyncio.gather(*read_save_tasks)
    
    # Wait for all tag generation tasks to complete
    tag_results = []
    if generate_tags and tag_gen_tasks:
        print("⏳ Waiting for tag generation to complete...")
        tag_results = await asyncio.gather(*tag_gen_tasks)
    
    end_time = time.perf_counter()
    total_time = end_time - start_time
    
    print(f"\n✅ All file processing completed in {total_time:.4f} seconds.")
    
    # Display results
    print("\n" + "="*80)
    print("📄 FILE CONTENTS")
    print("="*80)
    
    successful_reads = 0
    failed_reads = 0
    
    for result in read_results:
        filename = os.path.basename(result["path"])
        
        if result["status"] == "error":
            failed_reads += 1
            print(f"\n❌ {filename}")
            print(f"   {result['error']}")
        else:
            successful_reads += 1
            content = result["content"]
            print(f"\n📖 {filename} ({len(content)} characters, {result['lines']} lines)")
            print("-" * 40)
            
            # Display content with line numbers - SHOW ALL LINES (no truncation)
            lines = content.split('\n')
            
            if max_display_lines and len(lines) > max_display_lines:
                # Only truncate if explicitly requested via parameter
                print(f"First {max_display_lines} lines (of {len(lines)} total):")
                for i, line in enumerate(lines[:max_display_lines], 1):
                    print(f"{i:3d}: {line}")
                print(f"... ({len(lines) - max_display_lines} more lines)")
                print(f"💡 Use --max-lines 0 to see all lines or check context/{filename}.content.json")
            else:
                # Show complete file content
                for i, line in enumerate(lines, 1):
                    print(f"{i:3d}: {line}")
    
    # Create processing summary
    summary = {
        "processed_files": len(file_paths),
        "successful_reads": successful_reads,
        "failed_reads": failed_reads,
        "tag_generation_enabled": generate_tags,
        "ctags_available": ctags_available,
        "processing_time_seconds": total_time,
        "context_folder": context_dir,
        "read_results": read_results,
        "tag_results": tag_results if generate_tags else []
    }
    
    summary_path = os.path.join(context_dir, "processing_summary.json")
    async with aiofiles.open(summary_path, mode='w', encoding='utf-8') as f:
        await f.write(json.dumps(summary, indent=2))
    
    # Summary
    print("\n" + "="*80)
    print("📊 SUMMARY")
    print("="*80)
    print(f"Total files processed: {len(file_paths)}")
    print(f"Successfully read: {successful_reads}")
    print(f"Failed to read: {failed_reads}")
    if generate_tags:
        successful_tags = len([r for r in tag_results if r["status"] == "success"])
        print(f"Tags generated: {successful_tags}/{len(file_paths)}")
    print(f"Total processing time: {total_time:.4f} seconds")
    print(f"Average time per file: {total_time/len(file_paths):.4f} seconds")
    print(f"📁 Context folder: {context_dir}")
    print(f"📊 Processing summary: {summary_path}")

def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import aiofiles
        return True
    except ImportError:
        print("❌ Error: The 'aiofiles' library is not installed.")
        print("📦 Please install it using: pip install aiofiles")
        return False

if __name__ == "__main__":
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Concurrent File Reader v5 - Full Content Display")
    parser.add_argument("files", nargs="*", help="File paths to read (if none provided, reads workspace files)")
    parser.add_argument("--max-workers", type=int, default=8, help="Max threads for tag generation (default: 8)")
    parser.add_argument("--no-tags", action="store_true", help="Disable tag generation")
    parser.add_argument("--max-lines", type=int, default=0, help="Max lines to display per file (0 = show all, default: 0)")
    
    args = parser.parse_args()
    
    # Get file paths
    user_file_paths = args.files
    
    # Convert relative paths to absolute paths if provided
    if user_file_paths:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        absolute_paths = []
        
        for filepath in user_file_paths:
            if os.path.isabs(filepath):
                absolute_paths.append(filepath)
            else:
                # Check if file exists relative to script directory
                relative_to_script = os.path.join(script_dir, filepath)
                if os.path.exists(relative_to_script):
                    absolute_paths.append(relative_to_script)
                elif os.path.exists(filepath):
                    absolute_paths.append(os.path.abspath(filepath))
                else:
                    absolute_paths.append(filepath)  # Let the error handling deal with it
        
        user_file_paths = absolute_paths
    
    # Set max display lines (0 means show all)
    max_display_lines = args.max_lines if args.max_lines > 0 else None
    
    # Run the main coroutine
    try:
        asyncio.run(main(user_file_paths, args.max_workers, not args.no_tags, max_display_lines))
    except KeyboardInterrupt:
        print("\n⏹️ Operation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1) 