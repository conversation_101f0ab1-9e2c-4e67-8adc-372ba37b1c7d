#!/usr/bin/env python3
import re
import os
from pathlib import Path
import argparse
from datetime import datetime

def create_backup(file_path):
    file_path = Path(file_path)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_backup_{timestamp}{file_path.suffix}"
    backup_path = file_path.parent / backup_name
    with open(file_path, "r", encoding="utf-8") as src, open(backup_path, "w", encoding="utf-8") as dst:
        dst.write(src.read())
    print(f"[✔] Backup created: {backup_path}")
    return backup_path

def extract_css_blocks_from_text(text, start_line_offset=0):
    lines = text.splitlines()
    blocks = []
    selector = None
    properties = []
    start_line = None
    inside_block = False

    for i, line in enumerate(lines, start=1):
        stripped = line.strip()
        if not inside_block and '{' in stripped:
            selector = stripped.split('{')[0].strip()
            start_line = start_line_offset + i
            prop_part = stripped.split('{',1)[1].strip()
            properties = [prop_part] if prop_part else []
            inside_block = True
        elif inside_block:
            if '}' in stripped:
                prop_line = stripped.split('}')[0].strip()
                if prop_line:
                    properties.append(prop_line)
                blocks.append({
                    "selector": selector,
                    "properties": "\n".join(properties),
                    "start_line": start_line,
                    "length": (start_line_offset + i) - start_line + 1
                })
                inside_block = False
                selector = None
                properties = []
                start_line = None
            else:
                properties.append(line.rstrip('\n'))
    return blocks

def extract_inline_styles_from_lines(lines):
    inline_styles = []
    for i, line in enumerate(lines, start=1):
        for match in re.finditer(r'style\s*=\s*"(.*?)"', line, re.IGNORECASE):
            inline_styles.append({
                "line": i,
                "style": match.group(1)
            })
    return inline_styles

def extract_from_html(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    content = "".join(lines)

    style_blocks = []
    for match in re.finditer(r'<style[^>]*>(.*?)</style>', content, re.DOTALL | re.IGNORECASE):
        start_pos = match.start(1)
        start_line = content[:start_pos].count("\n") + 1
        css_text = match.group(1)
        blocks = extract_css_blocks_from_text(css_text, start_line)
        style_blocks.extend(blocks)

    script_blocks = []
    for match in re.finditer(r'<script[^>]*>(.*?)</script>', content, re.DOTALL | re.IGNORECASE):
        start_pos = match.start(1)
        start_line = content[:start_pos].count("\n") + 1
        js_text = match.group(1)
        script_blocks.append({
            "type": "script",
            "content": js_text.strip(),
            "start_line": start_line,
            "length": js_text.count("\n") + 1
        })

    inline_styles = extract_inline_styles_from_lines(lines)
    return style_blocks, script_blocks, inline_styles

def save_blocks(blocks, output_dir, prefix="block"):
    os.makedirs(output_dir, exist_ok=True)
    for i, block in enumerate(blocks):
        safe_selector = re.sub(r'[^\w\-]+', '_', block.get("selector", prefix))[:50] or f"{prefix}_{i}"
        filename = f"{i:03d}_{safe_selector}.css"
        file_path = os.path.join(output_dir, filename)
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(f"{block['selector']} {{\n{block['properties']}\n}}\n")
    print(f"[✔] Extracted {len(blocks)} CSS blocks to '{output_dir}'")

def save_script_blocks(blocks, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    for i, block in enumerate(blocks):
        filename = f"{i:03d}_script.js"
        file_path = os.path.join(output_dir, filename)
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(block["content"])
        print(f"[✔] Saved script block {i} to {file_path}")

def main():
    parser = argparse.ArgumentParser(
        description="Extract CSS and JavaScript blocks (with line numbers) from CSS or HTML files.\n\n"
                    "Examples:\n"
                    "  python3 extract_css_blocks.py styles.css\n"
                    "  python3 extract_css_blocks.py index.html --split-css css_parts --split-js js_parts\n"
                    "  python3 extract_css_blocks.py index.html --show-inline\n",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("file", help="Path to the CSS or HTML file to process")
    parser.add_argument("--split-css", metavar="DIR", help="Directory to save extracted CSS blocks as separate files")
    parser.add_argument("--split-js", metavar="DIR", help="Directory to save extracted JavaScript blocks as separate files")
    parser.add_argument("--show-inline", action="store_true", help="Show inline style attributes found in HTML elements")
    args = parser.parse_args()

    create_backup(args.file)

    ext = Path(args.file).suffix.lower()
    if ext == ".css":
        blocks = extract_css_blocks_from_text(open(args.file, encoding="utf-8").read())
        print(f"[✔] Extracted {len(blocks)} CSS blocks from CSS file.")
        if args.split_css:
            save_blocks(blocks, args.split_css)
        else:
            for b in blocks:
                print(f"\nSelector: {b['selector']}\nStart line: {b['start_line']}\nLength: {b['length']} lines")
                print(f"Properties:\n{b['properties']}\n{'='*40}")
    elif ext in {".html", ".htm"}:
        style_blocks, script_blocks, inline_styles = extract_from_html(args.file)
        print(f"[✔] Extracted {len(style_blocks)} CSS blocks from <style> tags.")
        print(f"[✔] Extracted {len(script_blocks)} JS blocks from <script> tags.")
        print(f"[✔] Found {len(inline_styles)} inline style attributes.")

        if args.split_css and style_blocks:
            save_blocks(style_blocks, args.split_css)
        else:
            for b in style_blocks:
                print(f"\nSelector: {b['selector']}\nStart line: {b['start_line']}\nLength: {b['length']} lines")
                print(f"Properties:\n{b['properties']}\n{'='*40}")

        if args.split_js and script_blocks:
            save_script_blocks(script_blocks, args.split_js)
        else:
            for i, b in enumerate(script_blocks):
                print(f"\nScript block #{i}\nStart line: {b['start_line']}\nLength: {b['length']} lines")
                print(f"Content:\n{b['content']}\n{'='*40}")

        if args.show_inline:
            print("\nInline Styles:")
            for item in inline_styles:
                print(f"Line {item['line']}: style=\"{item['style']}\"")

    else:
        print(f"Unsupported file extension: {ext}. Only .css and .html supported.")

if __name__ == "__main__":
    main()
